/* Import multicolor glow animations */
@import './animations.css';
/* Import pixelated background effects */
@import './pixelated-background.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Light Mode Colors - Soft, warm palette with pastel gradients */
  --bg-primary: #fef6f3; /* Soft warm cream */
  --bg-secondary: #fde8e4; /* Warm peach */
  --bg-tertiary: #fcd5ce; /* Light coral */
  --bg-highlight: #f9c6c9; /* Soft pink */

  --fg-primary: #4a3432; /* Warm dark brown */
  --fg-secondary: #6b4c4a; /* Medium brown */
  --fg-tertiary: #8b6765; /* Muted rose brown */
  --fg-accent: #d4516f; /* Soft magenta */

  --border-primary: rgba(212, 81, 111, 0.2); /* Soft pink border */
  --border-secondary: rgba(180, 100, 120, 0.3); /* Rose border */
  --border-accent: #e8a5b7; /* Light pink accent */
  
  /* Light mode glass effects */
  --glass-bg-light: rgba(255, 248, 245, 0.4);
  --glass-border-light: rgba(251, 207, 208, 0.3);
  
  /* Light mode gradients - pastel versions */
  --gradient-blue-light: #a8d5ff; /* Soft sky blue */
  --gradient-purple-light: #dcc9ff; /* Soft lavender */
  --gradient-cyan-light: #b8f0ff; /* Soft aqua */
  --gradient-pink-light: #ffc4e1; /* Soft pink */
  --gradient-amber-light: #ffe4a8; /* Soft amber */
  --gradient-coral-light: #ffb8a8; /* Soft coral */

  /* Example for one agent - extend as needed */
  --agent-dataCollector-background: #DBEAFE;
  --agent-dataCollector-border: #93C5FD;
  --agent-dataCollector-text: #1E40AF;
  --agent-dataCollector-accent: #60A5FA;

  --agent-financialAnalyst-background: #E6FFFA;
  --agent-financialAnalyst-border: #A7F3D0;
  --agent-financialAnalyst-text: #047857;
  --agent-financialAnalyst-accent: #34D399;

  --agent-riskAssessment-background: #FFFBEB;
  --agent-riskAssessment-border: #FDE68A;
  --agent-riskAssessment-text: #B45309;
  --agent-riskAssessment-accent: #FBBF24;

  --agent-compliance-background: #F3E8FF;
  --agent-compliance-border: #D8B4FE;
  --agent-compliance-text: #6B21A8;
  --agent-compliance-accent: #A78BFA;

  --agent-legalReview-background: #E0E7FF;
  --agent-legalReview-border: #A5B4FC;
  --agent-legalReview-text: #3730A3;
  --agent-legalReview-accent: #818CF8;

  --agent-entityMapping-background: #FFF3E0;
  --agent-entityMapping-border: #FFCC80;
  --agent-entityMapping-text: #E65100;
  --agent-entityMapping-accent: #FFA726;

  --agent-reportInsights-background: #E0F2F7;
  --agent-reportInsights-border: #A7F3D0;
  --agent-reportInsights-text: #0D9488;
  --agent-reportInsights-accent: #2DD4BF;

  --agent-dueDiligenceOrchestrator-background: #FEE2E2;
  --agent-dueDiligenceOrchestrator-border: #FCA5A5;
  --agent-dueDiligenceOrchestrator-text: #B91C1C;
  --agent-dueDiligenceOrchestrator-accent: #F87171;

  --agent-parallelAnalysis-background: #E0F7FA;
  --agent-parallelAnalysis-border: #A6EEF2;
  --agent-parallelAnalysis-text: #0E7490;
  --agent-parallelAnalysis-accent: #22D3EE;

  --status-success-bg: #D1FAE5;
  --status-error-bg: #FEE2E2;
  --status-warning-bg: #FEF3C7;
  --status-info-bg: #DBEAFE;
}

/* Light Mode Agent Colors - Pastel versions */
:root {
  /* DataCollector - Soft blue */
  --agent-dataCollector-background: #e0f2fe;
  --agent-dataCollector-border: #7dd3fc;
  --agent-dataCollector-text: #075985;
  --agent-dataCollector-accent: #38bdf8;

  /* FinancialAnalyst - Soft green */
  --agent-financialAnalyst-background: #dcfce7;
  --agent-financialAnalyst-border: #86efac;
  --agent-financialAnalyst-text: #166534;
  --agent-financialAnalyst-accent: #4ade80;

  /* RiskAssessment - Soft amber */
  --agent-riskAssessment-background: #fef3c7;
  --agent-riskAssessment-border: #fde68a;
  --agent-riskAssessment-text: #92400e;
  --agent-riskAssessment-accent: #fbbf24;

  /* Compliance - Soft purple */
  --agent-compliance-background: #f3e8ff;
  --agent-compliance-border: #e9d5ff;
  --agent-compliance-text: #6b21a8;
  --agent-compliance-accent: #c084fc;

  /* LegalReview - Soft indigo */
  --agent-legalReview-background: #e0e7ff;
  --agent-legalReview-border: #c7d2fe;
  --agent-legalReview-text: #4338ca;
  --agent-legalReview-accent: #818cf8;

  /* EntityMapping - Soft orange */
  --agent-entityMapping-background: #fed7aa;
  --agent-entityMapping-border: #fdba74;
  --agent-entityMapping-text: #c2410c;
  --agent-entityMapping-accent: #fb923c;

  /* ReportInsights - Soft teal */
  --agent-reportInsights-background: #ccfbf1;
  --agent-reportInsights-border: #5eead4;
  --agent-reportInsights-text: #134e4a;
  --agent-reportInsights-accent: #2dd4bf;

  /* DueDiligenceOrchestrator - Soft rose */
  --agent-dueDiligenceOrchestrator-background: #ffe4e6;
  --agent-dueDiligenceOrchestrator-border: #fecdd3;
  --agent-dueDiligenceOrchestrator-text: #be123c;
  --agent-dueDiligenceOrchestrator-accent: #fb7185;

  /* ParallelAnalysis - Soft cyan */
  --agent-parallelAnalysis-background: #cffafe;
  --agent-parallelAnalysis-border: #67e8f9;
  --agent-parallelAnalysis-text: #155e75;
  --agent-parallelAnalysis-accent: #22d3ee;
}

/* Dark Mode Colors */
.dark {
  /* Dark Mode Agent Colors */
  --agent-dataCollector-background: #1E3A8A;
  --agent-dataCollector-border: #3B82F6;
  --agent-dataCollector-text: #DBEAFE;
  --agent-dataCollector-accent: #60A5FA;

  --agent-financialAnalyst-background: #047857;
  --agent-financialAnalyst-border: #10B981;
  --agent-financialAnalyst-text: #D1FAE5;
  --agent-financialAnalyst-accent: #34D399;

  --agent-riskAssessment-background: #B45309;
  --agent-riskAssessment-border: #F59E0B;
  --agent-riskAssessment-text: #FEF3C7;
  --agent-riskAssessment-accent: #FBBF24;

  --agent-compliance-background: #6B21A8;
  --agent-compliance-border: #8B5CF6;
  --agent-compliance-text: #F3E8FF;
  --agent-compliance-accent: #A78BFA;

  --agent-legalReview-background: #3730A3;
  --agent-legalReview-border: #6366F1;
  --agent-legalReview-text: #E0E7FF;
  --agent-legalReview-accent: #818CF8;

  --agent-entityMapping-background: #E65100;
  --agent-entityMapping-border: #FF9800;
  --agent-entityMapping-text: #FFF3E0;
  --agent-entityMapping-accent: #FFA726;

  --agent-reportInsights-background: #0D9488;
  --agent-reportInsights-border: #14B8A6;
  --agent-reportInsights-text: #E0F2F7;
  --agent-reportInsights-accent: #2DD4BF;

  --agent-dueDiligenceOrchestrator-background: #B91C1C;
  --agent-dueDiligenceOrchestrator-border: #EF4444;
  --agent-dueDiligenceOrchestrator-text: #FEE2E2;
  --agent-dueDiligenceOrchestrator-accent: #F87171;

  --agent-parallelAnalysis-background: #0E7490;
  --agent-parallelAnalysis-border: #06B6D4;
  --agent-parallelAnalysis-text: #E0F7FA;
  --agent-parallelAnalysis-accent: #22D3EE;

  --status-success-bg: #065F46;
  --status-error-bg: #991B1B;
  --status-warning-bg: #92400E;
  --status-info-bg: #1E40AF;
}


/* Enhanced theme transition styles */
*, *::before, *::after {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smoother transitions for specific elements */
html, body {
  transition-property: background-color, color;
  transition-duration: 400ms;
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced transitions for interactive elements */
button, input, textarea, select {
  transition-property: background-color, border-color, color, box-shadow, transform;
  transition-duration: 250ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.no-transition,
.no-transition *,
.no-transition *::before,
.no-transition *::after {
  transition: none !important;
}

@layer base {
  /* Font fallbacks for Docker builds */
  :root {
    --font-sans: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    --font-mono: ui-monospace, SFMono-Regular, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  }

  body {
    /* Make body transparent to show pixelated background */
    @apply text-foreground;
    background: transparent !important;
    font-family: var(--font-sans);
  }

  /* Ensure main containers are transparent */
  #__next {
    background: transparent !important;
  }

  /* Font family fallbacks */
  .font-sans {
    font-family: var(--font-sans);
  }

  .font-mono {
    font-family: var(--font-mono);
  }
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #a855f7);
  border-radius: 5px;
  border: 2px solid transparent;
  background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #60a5fa, #c084fc);
  background-clip: padding-box;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #3b82f6 rgba(0, 0, 0, 0.1);
}

/* Enhanced focus states */
*:focus-visible {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced selection */
::selection {
  background: rgba(168, 85, 247, 0.3);
  color: white;
}

/* Override HeroUI backgrounds to be transparent */
.dark {
  --background: transparent;
  --content1: rgba(18, 18, 18, 0.5);
  --content2: rgba(27, 27, 27, 0.5);
  --content3: rgba(35, 35, 35, 0.5);
  --content4: rgba(43, 43, 43, 0.5);
}

/* Light mode overrides */
.light {
  --background: transparent;
  --content1: rgba(255, 248, 245, 0.5);
  --content2: rgba(253, 232, 228, 0.5);
  --content3: rgba(252, 213, 206, 0.5);
  --content4: rgba(249, 198, 201, 0.5);
}

/* Light mode glass panel effects */
.light .glass-panel {
  background: var(--glass-bg-light) !important;
  backdrop-filter: blur(12px) saturate(1.2);
  -webkit-backdrop-filter: blur(12px) saturate(1.2);
  border: none !important;
  box-shadow:
    0 8px 32px 0 rgba(251, 207, 208, 0.2),
    inset 0 0 0 1px rgba(255, 255, 255, 0.6);
}

/* Fix the HeroUI Tabs base component that's causing the border */
.light [data-slot="base"].inline-flex {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Target the specific tabs base wrapper */
.light div[data-slot="base"].inline-flex {
  border: none !important;
}

/* Ensure no borders on the tabs container in light mode */
.light [data-component="tabs"] [data-slot="base"] {
  border: none !important;
}

.light .glass-panel-dark {
  background: rgba(255, 241, 235, 0.6) !important;
  backdrop-filter: blur(12px) saturate(1.2);
  -webkit-backdrop-filter: blur(12px) saturate(1.2);
  border: 1px solid rgba(251, 207, 208, 0.4) !important;
  box-shadow:
    0 8px 32px 0 rgba(251, 207, 208, 0.3),
    inset 0 0 0 1px rgba(255, 255, 255, 0.7);
}

/* Light mode text colors */
.light {
  color: var(--fg-primary);
}

.light .text-foreground-300,
.light .text-foreground-400,
.light .text-foreground-500,
.light .text-foreground-600,
.light .text-foreground-700 {
  color: var(--fg-secondary) !important;
}

.light .text-white {
  color: var(--fg-primary) !important;
}

/* Light mode gradient text */
.light .bg-gradient-to-r {
  background: linear-gradient(to right, var(--gradient-blue-light), var(--gradient-purple-light)) !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
}

/* Light mode gradient backgrounds */
.light .from-blue-500 {
  --tw-gradient-from: var(--gradient-blue-light) !important;
}

.light .to-purple-500 {
  --tw-gradient-to: var(--gradient-purple-light) !important;
}

.light .from-blue-500\/20 {
  --tw-gradient-from: rgba(168, 213, 255, 0.2) !important;
}

.light .to-purple-500\/20 {
  --tw-gradient-to: rgba(220, 201, 255, 0.2) !important;
}

/* Light mode borders */
.light .border-white\/10,
.light .border-white\/20,
.light .border-white\/30 {
  border-color: var(--border-primary) !important;
}

/* Light mode hover states */
.light .hover\:border-white\/30:hover {
  border-color: var(--border-secondary) !important;
}

/* Light mode scrollbar */
.light ::-webkit-scrollbar-track {
  background: rgba(251, 207, 208, 0.2);
}

.light ::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--gradient-blue-light), var(--gradient-purple-light));
  background-clip: padding-box;
}

.light ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--gradient-purple-light), var(--gradient-pink-light));
  background-clip: padding-box;
}

/* Light mode selection */
.light ::selection {
  background: rgba(212, 81, 111, 0.3);
  color: var(--fg-primary);
}

/* Ensure navbar is transparent */
nav {
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px);
}

.light nav {
  background: rgba(255, 241, 235, 0.5) !important;
  backdrop-filter: blur(10px);
}

/* Light mode specific component styles */
.light .bg-content1,
.light .bg-content2,
.light .bg-content3,
.light .bg-content4 {
  background: var(--glass-bg-light) !important;
}

/* Light mode button styles */
.light button {
  color: var(--fg-primary) !important;
}

.light button:hover {
  transform: scale(1.05);
}

/* Light mode input styles */
.light input,
.light textarea {
  color: var(--fg-primary) !important;
  background: rgba(255, 248, 245, 0.6) !important;
}

.light input::placeholder,
.light textarea::placeholder {
  color: var(--fg-tertiary) !important;
}

/* Light mode code blocks */
.light pre,
.light code {
  background: rgba(255, 241, 235, 0.8) !important;
  color: var(--fg-primary) !important;
}

/* Light mode status colors */
.light .text-green-400,
.light .text-green-300 {
  color: #059669 !important;
}

.light .text-yellow-400,
.light .text-yellow-300 {
  color: #d97706 !important;
}

.light .text-red-400,
.light .text-red-300 {
  color: #dc2626 !important;
}

.light .text-blue-400,
.light .text-blue-300 {
  color: #2563eb !important;
}

.light .text-purple-400,
.light .text-purple-300 {
  color: #7c3aed !important;
}

/* Light mode bg colors */
.light .bg-green-400 {
  background-color: #86efac !important;
}

.light .bg-yellow-400 {
  background-color: #fde047 !important;
}

.light .bg-red-400 {
  background-color: #fca5a5 !important;
}

/* Light mode shadows */
.light .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(212, 81, 111, 0.1), 0 4px 6px -2px rgba(212, 81, 111, 0.05) !important;
}

.light .shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(212, 81, 111, 0.1), 0 10px 10px -5px rgba(212, 81, 111, 0.04) !important;
}

/* Light mode neon effects */
.light .neon-border::before {
  background: linear-gradient(
    45deg,
    var(--gradient-blue-light),
    var(--gradient-purple-light),
    var(--gradient-pink-light),
    var(--gradient-blue-light)
  );
}

/* Light mode animations */
.light .animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .7;
  }
}

/* Let HeroUI theme handle all styling */

/* Performance optimization classes */
.contain-layout {
  contain: layout;
}

.contain-style {
  contain: style;
}

.contain-paint {
  contain: paint;
}

.contain-size {
  contain: size;
}

.contain-strict {
  contain: strict;
}

/* Virtual scrolling optimization */
.virtual-scroll-container {
  contain: strict;
  overflow: hidden;
}

.virtual-scroll-item {
  contain: layout style paint;
  will-change: transform;
}

/* Animation performance */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Memory optimization for large lists */
.memory-optimized {
  contain: layout style paint;
  content-visibility: auto;
  contain-intrinsic-size: 0 200px;
}

/* Smooth transitions with hardware acceleration */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

/* Prevent layout thrashing */
.stable-layout {
  contain: layout;
  min-height: 0;
  min-width: 0;
}

/* Optimized loading states */
.loading-skeleton {
  contain: layout style paint;
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

@keyframes skeleton-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

/* Debounced update indicators */
.updating {
  transition: opacity 0.15s ease-out;
}

.updating.loading {
  opacity: 0.7;
}

/* High-performance chart containers */
.chart-container {
  contain: layout style paint;
  isolation: isolate;
}

/* Optimized tooltip positioning */
.tooltip-container {
  contain: layout;
  position: relative;
  z-index: 1000;
}

/* Prevent reflow during animations */
.animation-container {
  contain: layout;
  transform: translateZ(0);
}

/* Anti-flickering optimizations for live data updates */
.metric-value {
  transition: none;
  animation: none;
}

.metric-progress {
  transition: width 500ms cubic-bezier(0.4, 0, 0.2, 1);
  will-change: width;
}

/* Prevent layout shifts */
.stable-height {
  min-height: var(--stable-height, auto);
}

/* Prevent flickering on rapid updates */
.no-flicker {
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

/* Smooth live data transitions */
.live-data-container {
  contain: layout style paint;
  will-change: contents;
}

.live-metric {
  transition: transform 200ms ease-out;
  contain: layout;
}

/* Optimized for frequent updates */
@media (prefers-reduced-motion: no-preference) {
  .smooth-update {
    transition: transform 200ms ease-out, opacity 200ms ease-out;
  }
}

/* Enhanced tab styling for better light/dark mode contrast */
.tab-button {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.tab-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(168, 85, 247, 0.1));
  opacity: 0;
  transition: opacity 300ms ease;
  z-index: -1;
}

.tab-button.active::before {
  opacity: 1;
}

/* Dark mode tab styling */
.dark .tab-button {
  color: rgb(156, 163, 175);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

.dark .tab-button:hover {
  color: rgb(229, 231, 235);
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

.dark .tab-button.active {
  color: rgb(255, 255, 255);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(168, 85, 247, 0.2));
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Light mode tab styling */
.light .tab-button {
  color: rgb(107, 114, 128);
  border: 1px solid rgba(212, 81, 111, 0.2);
  background: rgba(255, 248, 245, 0.6);
}

.light .tab-button:hover {
  color: rgb(55, 65, 81);
  background: rgba(255, 241, 235, 0.8);
  border-color: rgba(212, 81, 111, 0.3);
}

.light .tab-button.active {
  color: rgb(31, 41, 55);
  background: linear-gradient(135deg, rgba(168, 213, 255, 0.4), rgba(220, 201, 255, 0.4));
  border: 1px solid rgba(212, 81, 111, 0.4);
  box-shadow:
    0 4px 12px rgba(168, 213, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Enhanced glass effect for active tabs */
.tab-button.active {
  backdrop-filter: blur(12px) saturate(1.2);
  -webkit-backdrop-filter: blur(12px) saturate(1.2);
}

/* Icon styling within tabs */
.tab-button svg {
  transition: transform 200ms ease;
}

.tab-button:hover svg {
  transform: scale(1.1);
}

.tab-button.active svg {
  transform: scale(1.05);
}

/* Enhanced HeroUI Tabs styling for better light/dark mode contrast */
/* Dark mode HeroUI tabs */
.dark [data-slot="tabList"] {
  background: rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(12px) saturate(1.2);
  -webkit-backdrop-filter: blur(12px) saturate(1.2);
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.dark [data-slot="tab"] {
  color: rgb(156, 163, 175) !important;
  transition: all 300ms ease !important;
}

.dark [data-slot="tab"][data-hover="true"] {
  background: rgba(255, 255, 255, 0.05) !important;
  color: rgb(229, 231, 235) !important;
}

.dark [data-slot="tab"][data-selected="true"] {
  color: rgb(255, 255, 255) !important;
}

.dark [data-slot="cursor"] {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(168, 85, 247, 0.3)) !important;
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
}

/* Light mode HeroUI tabs */
.light [data-slot="tabList"] {
  background: rgba(255, 248, 245, 0.8) !important;
  backdrop-filter: blur(12px) saturate(1.2);
  -webkit-backdrop-filter: blur(12px) saturate(1.2);
  border: 1px solid rgba(212, 81, 111, 0.3) !important;
  box-shadow:
    0 4px 12px rgba(212, 81, 111, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

.light [data-slot="tab"] {
  color: rgb(107, 114, 128) !important;
  transition: all 300ms ease !important;
}

.light [data-slot="tab"][data-hover="true"] {
  background: rgba(255, 241, 235, 0.8) !important;
  color: rgb(55, 65, 81) !important;
}

.light [data-slot="tab"][data-selected="true"] {
  color: rgb(31, 41, 55) !important;
  font-weight: 600 !important;
}

.light [data-slot="cursor"] {
  background: linear-gradient(135deg, rgba(168, 213, 255, 0.6), rgba(220, 201, 255, 0.6)) !important;
  box-shadow:
    0 4px 12px rgba(168, 213, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(8px) saturate(1.2) !important;
  -webkit-backdrop-filter: blur(8px) saturate(1.2) !important;
  border: 1px solid rgba(212, 81, 111, 0.2) !important;
}

/* Enhanced tab content styling */
.light [data-slot="tabContent"] {
  color: inherit !important;
}

.dark [data-slot="tabContent"] {
  color: inherit !important;
}

/* Improved glass panel for header in light mode */
.light .glass-panel-dark {
  background: rgba(255, 248, 245, 0.85) !important;
  backdrop-filter: blur(16px) saturate(1.3);
  -webkit-backdrop-filter: blur(16px) saturate(1.3);
  border: 1px solid rgba(212, 81, 111, 0.2) !important;
  box-shadow:
    0 8px 32px 0 rgba(212, 81, 111, 0.15),
    inset 0 0 0 1px rgba(255, 255, 255, 0.8) !important;
}

/* Enhanced border styling for light mode */
.light .border-white\/10 {
  border-color: rgba(212, 81, 111, 0.2) !important;
}

.light .border-white\/20 {
  border-color: rgba(212, 81, 111, 0.3) !important;
}

.light .hover\:border-white\/30:hover {
  border-color: rgba(212, 81, 111, 0.4) !important;
}

/* Enhanced modal styling for better light mode visibility */
.light [data-slot="base"] {
  background: rgba(255, 248, 245, 0.95) !important;
  backdrop-filter: blur(20px) saturate(1.3);
  -webkit-backdrop-filter: blur(20px) saturate(1.3);
  border: 1px solid rgba(212, 81, 111, 0.3) !important;
  box-shadow:
    0 25px 50px -12px rgba(212, 81, 111, 0.25),
    inset 0 0 0 1px rgba(255, 255, 255, 0.9) !important;
}

.light [data-slot="header"] {
  border-color: rgba(212, 81, 111, 0.2) !important;
  background: rgba(255, 241, 235, 0.8) !important;
}

.light [data-slot="body"] {
  background: rgba(255, 248, 245, 0.6) !important;
}

.light [data-slot="footer"] {
  border-color: rgba(212, 81, 111, 0.2) !important;
  background: rgba(255, 241, 235, 0.8) !important;
}

/* Enhanced text contrast for light mode */
.light .text-fg-primary {
  color: rgb(31, 41, 55) !important;
}

.light .text-fg-secondary {
  color: rgb(75, 85, 99) !important;
}

.light .text-fg-tertiary {
  color: rgb(107, 114, 128) !important;
}

/* Enhanced accordion styling for light mode */
.light [data-slot="base"].border {
  border-color: rgba(212, 81, 111, 0.3) !important;
  background: rgba(255, 248, 245, 0.8) !important;
}

.light [data-slot="trigger"] {
  background: rgba(255, 241, 235, 0.9) !important;
}

.light [data-slot="content"] {
  background: rgba(255, 248, 245, 0.95) !important;
}

/* Enhanced code block styling for light mode */
.light .bg-bg-secondary {
  background: rgba(255, 241, 235, 0.9) !important;
  border: 1px solid rgba(212, 81, 111, 0.2) !important;
}

/* Improved syntax highlighting for light mode */
.light .token.string {
  color: #22863a !important;
}

.light .token.number {
  color: #005cc5 !important;
}

.light .token.boolean {
  color: #d73a49 !important;
}

.light .token.null {
  color: #6f42c1 !important;
}

.light .token.property {
  color: #005cc5 !important;
}

.light .token.punctuation {
  color: #24292e !important;
}

/* Enhanced modal classes for better styling */
.modal-enhanced {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.light .modal-enhanced {
  box-shadow: 0 25px 50px -12px rgba(212, 81, 111, 0.25) !important;
}

.modal-header-enhanced {
  backdrop-filter: blur(12px) saturate(1.2);
  -webkit-backdrop-filter: blur(12px) saturate(1.2);
}

.modal-body-enhanced {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.modal-footer-enhanced {
  backdrop-filter: blur(12px) saturate(1.2);
  -webkit-backdrop-filter: blur(12px) saturate(1.2);
}

/* Enhanced text readability */
.light .modal-enhanced .text-lg {
  color: rgb(31, 41, 55) !important;
  font-weight: 600 !important;
}

.light .modal-enhanced .text-sm {
  color: rgb(75, 85, 99) !important;
}

.light .modal-enhanced .text-xs {
  color: rgb(107, 114, 128) !important;
}

/* Enhanced button styling in light mode */
.light .modal-enhanced button {
  font-weight: 500 !important;
}

.light .modal-enhanced button[data-hover="true"] {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 81, 111, 0.15);
}

/* Enhanced tab styling within modal */
.light .modal-enhanced .border-fg-accent {
  border-color: rgb(212, 81, 111) !important;
}

.light .modal-enhanced .text-fg-accent {
  color: rgb(212, 81, 111) !important;
  font-weight: 600 !important;
}

/* Enhanced chat interface styling */
.chat-message-card {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.chat-message-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.light .chat-message-card:hover {
  box-shadow: 0 8px 25px rgba(212, 81, 111, 0.1);
}

/* Agent-specific gradient backgrounds for light mode */
.light .bg-gradient-to-r.from-emerald-50 {
  background: linear-gradient(to right, #ecfdf5, #f0fdfa) !important;
}

.light .bg-gradient-to-r.from-green-50 {
  background: linear-gradient(to right, #f0fdf4, #ecfdf5) !important;
}

.light .bg-gradient-to-r.from-amber-50 {
  background: linear-gradient(to right, #fffbeb, #fef3c7) !important;
}

.light .bg-gradient-to-r.from-purple-50 {
  background: linear-gradient(to right, #faf5ff, #f3e8ff) !important;
}

.light .bg-gradient-to-r.from-blue-50 {
  background: linear-gradient(to right, #eff6ff, #dbeafe) !important;
}

.light .bg-gradient-to-r.from-cyan-50 {
  background: linear-gradient(to right, #ecfeff, #cffafe) !important;
}

.light .bg-gradient-to-r.from-teal-50 {
  background: linear-gradient(to right, #f0fdfa, #ccfbf1) !important;
}

/* Enhanced prose styling for chat messages */
.chat-prose {
  line-height: 1.7;
  font-size: 0.95rem;
}

.light .chat-prose {
  color: rgb(55, 65, 81) !important;
}

/* Enhanced chip styling in chat */
.chat-chip {
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* Smooth scroll behavior for chat */
.chat-scroll {
  scroll-behavior: smooth;
  scrollbar-width: thin;
}

.chat-scroll::-webkit-scrollbar {
  width: 6px;
}

.chat-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.light .chat-scroll::-webkit-scrollbar-track {
  background: rgba(212, 81, 111, 0.05);
}

.light .chat-scroll::-webkit-scrollbar-thumb {
  background: rgba(212, 81, 111, 0.2);
}

.light .chat-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 81, 111, 0.3);
}

/* Enhanced empty state styling */
.chat-empty-state {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced header backdrop */
.chat-header {
  backdrop-filter: blur(12px) saturate(1.2);
  -webkit-backdrop-filter: blur(12px) saturate(1.2);
}

.light .chat-header {
  background: rgba(255, 248, 245, 0.9) !important;
  border-color: rgba(212, 81, 111, 0.2) !important;
}

/* Enhanced input area */
.chat-input-area {
  backdrop-filter: blur(16px) saturate(1.3);
  -webkit-backdrop-filter: blur(16px) saturate(1.3);
}

.light .chat-input-area {
  background: rgba(255, 248, 245, 0.95) !important;
  border-color: rgba(212, 81, 111, 0.2) !important;
}
