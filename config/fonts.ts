// Robust font configuration that works in all environments
const isDockerBuild = process.env.DOCKER_BUILD === 'true' || process.env.NEXT_FONT_OFFLINE === 'true';

// System font fallbacks
const systemFontFallbacks = {
  sans: ["system-ui", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif"],
  mono: ["ui-monospace", "SFMono-Regular", "Monaco", "Consolas", "Liberation Mono", "Courier New", "monospace"],
};

let fontSans: any;
let fontMono: any;

if (isDockerBuild) {
  // Use system fonts for Docker builds
  fontSans = {
    variable: "--font-sans",
    className: "font-sans",
    style: { fontFamily: systemFontFallbacks.sans.join(", ") },
  };

  fontMono = {
    variable: "--font-mono",
    className: "font-mono",
    style: { fontFamily: systemFontFallbacks.mono.join(", ") },
  };
} else {
  // Try to load Google Fonts for normal builds
  try {
    const googleFonts = require("next/font/google");

    fontSans = googleFonts.Inter({
      subsets: ["latin"],
      variable: "--font-sans",
      display: "swap",
      fallback: systemFontFallbacks.sans,
    });

    fontMono = googleFonts.Fira_Code({
      subsets: ["latin"],
      variable: "--font-mono",
      display: "swap",
      fallback: systemFontFallbacks.mono,
    });
  } catch (error) {
    console.warn("Failed to load Google Fonts, using system fonts:", error);

    // Fallback to system fonts
    fontSans = {
      variable: "--font-sans",
      className: "font-sans",
      style: { fontFamily: systemFontFallbacks.sans.join(", ") },
    };

    fontMono = {
      variable: "--font-mono",
      className: "font-mono",
      style: { fontFamily: systemFontFallbacks.mono.join(", ") },
    };
  }
}

export { fontMono, fontSans };

