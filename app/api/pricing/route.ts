import { NextRequest, NextResponse } from 'next/server';

/**
 * API route to proxy LiteLLM pricing data
 * This solves CORS/CSP issues by fetching data server-side
 */

const PRICING_URLS = [
  'https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json',
  'https://api.github.com/repos/BerriAI/litellm/contents/model_prices_and_context_window.json',
];

export async function GET(request: NextRequest) {
  try {
    console.log('API: Fetching pricing data server-side...');

    // Try each URL in sequence
    for (const url of PRICING_URLS) {
      try {
        console.log(`API: Attempting to fetch from: ${url}`);
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
        
        const response = await fetch(url, {
          headers: {
            'Accept': 'application/json',
            'Cache-Control': 'no-cache',
            'User-Agent': 'Agent-Development-Kit-Frontend/1.0',
          },
          signal: controller.signal,
        });
        
        clearTimeout(timeoutId);

        if (!response.ok) {
          console.warn(`API: HTTP error from ${url}: ${response.status} ${response.statusText}`);
          continue;
        }

        let data;
        
        // Handle GitHub API response (base64 encoded)
        if (url.includes('api.github.com')) {
          const githubResponse = await response.json();
          if (githubResponse.content && githubResponse.encoding === 'base64') {
            const decodedContent = Buffer.from(githubResponse.content, 'base64').toString('utf-8');
            data = JSON.parse(decodedContent);
            console.log(`API: ✅ Successfully decoded GitHub API response from: ${url}`);
          } else {
            console.warn(`API: Unexpected GitHub API response format from: ${url}`);
            continue;
          }
        } else {
          // Handle raw GitHub response
          data = await response.json();
          console.log(`API: ✅ Successfully fetched raw data from: ${url}`);
        }

        // Validate the data structure
        if (!data || typeof data !== 'object') {
          console.warn(`API: Invalid data structure from: ${url}`);
          continue;
        }

        const modelCount = Object.keys(data).length;
        console.log(`API: Successfully fetched ${modelCount} models from: ${url}`);

        // Return successful response with caching headers
        return NextResponse.json(data, {
          headers: {
            'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400', // Cache for 1 hour, stale for 1 day
            'Content-Type': 'application/json',
          },
        });

      } catch (error) {
        console.warn(`API: Failed to fetch from ${url}:`, error);
        
        // Log specific error details
        if (error instanceof Error) {
          if (error.name === 'AbortError') {
            console.log('API: Request timed out');
          } else if (error.message.includes('fetch')) {
            console.log('API: Network or connectivity issue');
          } else {
            console.log(`API: Unexpected error: ${error.message}`);
          }
        }
        
        continue;
      }
    }

    // If all URLs failed, return error
    console.error('API: All pricing data sources failed');
    return NextResponse.json(
      { 
        error: 'Failed to fetch pricing data from all sources',
        sources: PRICING_URLS,
        timestamp: new Date().toISOString(),
      }, 
      { 
        status: 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );

  } catch (error) {
    console.error('API: Unexpected error in pricing route:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      }, 
      { 
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );
  }
}

// Optional: Add POST method for future enhancements
export async function POST(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not allowed' }, 
    { status: 405 }
  );
}
