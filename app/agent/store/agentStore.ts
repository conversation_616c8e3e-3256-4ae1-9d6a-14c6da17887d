import { create, <PERSON><PERSON><PERSON> } from "zustand";

import { AGENT_CONFIGS } from "../config/agentConfig"; // Added
import * as sessionService from "../services/sessionService";
import {
  AgentName, // Added
  AgentState,
  ConnectionState,
  EventFunctionResponse,
  EventHistoryItem,
  EventType,
  SSEEvent,
  TabKey,
} from "../types";
import { formatTimestamp, isAgentActive } from "../utils";
import { addSessionToHistory } from "../utils/sessionHistoryUtils";

const NEXT_PUBLIC_BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL;

let appNameFetchPromise: Promise<void> | null = null;
let sessionInitializationPromise: Promise<{ userId: string; sessionId: string }> | null = null;
let promiseCreationCounter = 0; // For debugging unique promise instances

// Define the state structure without the action methods
interface AgentStoreState {
  // Agent State
  agentState: AgentState;
  // Connection State
  connectionState: ConnectionState;
  // Event History
  eventHistory: EventHistoryItem[];
  // Filtering options
  filterType: string;
  filterAgent: AgentName | "all"; // Updated type
  filterToolName: string;
  showOnlyErrors: boolean;
  useRelativeTime: boolean;
  // Agent tracking
  activeAgent: AgentName | null; // Updated type
  // Active tab
  activeTab: TabKey;
  // Session management
  userId: string | null; // Added for dynamic user ID
  sessionId: string | null;
  isSessionInitializationAttempted: boolean; // Moved here for state
  isSessionInitialized: boolean;
  sessionInitializationError: string | null;
  // App metadata
  appName: string | null;
  availableApps: string[] | null;
  // Time handling
  serverTimeOffset: number;
  isAppNameInitialized: boolean;

  // Agent Graph Data (for StateTab)
  agentGraphDotSource: string | null;
  isFetchingGraph: boolean;
  graphFetchError: string | null;
}

export interface AgentStore extends AgentStoreState {
  // Agent State
  updateAgentState: (stateDelta: Partial<AgentState>) => void;
  resetAgentState: (agentId?: AgentName) => void; // Updated type for agentId

  // Connection State
  setIsConnected: (isConnected: boolean) => void;
  setConnectionError: (error: string | null) => void;
  incrementReconnectCount: () => void;
  resetConnectionState: () => void;

  // Event History
  addEvent: (event: SSEEvent) => void;
  clearEventHistory: () => void;
  filterEvents: (filters: {
    author?: AgentName; // Updated type
    type?: EventType;
    toolName?: string;
    isError?: boolean;
  }) => EventHistoryItem[];
  getEventsByType: (type: EventType) => EventHistoryItem[];
  getToolCalls: () => EventHistoryItem[];
  getToolResponses: () => EventHistoryItem[];

  // Phase 3: Enhanced filtering and navigation
  setFilterType: (type: string) => void;
  setFilterAgent: (agent: AgentName | "all") => void; // Updated type
  setFilterToolName: (tool: string) => void;
  setShowOnlyErrors: (show: boolean) => void;
  setUseRelativeTime: (use: boolean) => void;
  scrollToEvent: (eventId: string) => void;
  getFilteredEvents: () => EventHistoryItem[];

  // Agent Tracking
  activeAgent: AgentName | null; // Type already updated in AgentStoreState
  setActiveAgent: (agent: AgentName | null) => void; // Updated type
  isAgentActive: (agentName: AgentName) => boolean; // Updated type
  getAgentStatus: (agentName: AgentName) => "idle" | "active" | "completed" | "error"; // Updated type

  // Active Tab
  activeTab: TabKey;
  setActiveTab: (tab: TabKey) => void;

  // Session ID & User ID
  userId: string | null;
  sessionId: string | null;
  // isSessionInitializationAttempted: boolean; // This is part of state, not an action signature here
  initializeSessionId: () => Promise<{ userId: string; sessionId: string }>; // Will also initialize userId
  setSessionId: (sessionId: string | null) => void;
  setUserId: (userId: string | null) => void;

  // App Name and List
  appName: string | null;
  availableApps: string[] | null;
  fetchAndSetAppName: () => Promise<void>;
  setAppName: (appName: string | null) => void;
  setAvailableApps: (availableApps: string[] | null) => void;

  // Agent Graph Data (for StateTab)
  fetchAgentGraph: (eventId: string) => Promise<void>;
}

// Define initial state with proper explicit typing - no circular references
const initialStoreState: AgentStoreState = {
  // Safe initialization with empty objects - these will be filled during app usage
  agentState: {} as AgentState,
  connectionState: {
    isConnected: false,
    error: null,
    reconnectCount: 0,
  } as ConnectionState,
  eventHistory: [] as EventHistoryItem[],
  activeAgent: null as AgentName | null, // Updated type
  activeTab: "events" as TabKey,
  userId: null as string | null, // Initial state for userId
  sessionId: null as string | null,
  isSessionInitializationAttempted: false, // Initial state for flag
  isSessionInitialized: false,
  sessionInitializationError: null,
  appName: null as string | null,
  availableApps: null as string[] | null,
  // Time handling
  serverTimeOffset: 0,
  // Phase 3: Enhanced filtering defaults
  filterType: "all",
  filterAgent: "all" as AgentName | "all", // Updated type
  filterToolName: "all",
  showOnlyErrors: false,
  useRelativeTime: true,
  isAppNameInitialized: false,
  // Agent Graph Data initial state
  agentGraphDotSource: null,
  isFetchingGraph: false,
  graphFetchError: null,
};

// Create the store with proper typing
export const useAgentStore = create<AgentStore>((set, get) => ({
  ...initialStoreState,

  // Agent State actions
  updateAgentState: (stateDelta: Partial<AgentState>) => {
    const state = get();

    set({
      agentState: {
        ...state.agentState,
        ...stateDelta,
      },
    });
  },
  resetAgentState: (agentId?: AgentName) => {
    if (agentId && AGENT_CONFIGS[agentId]?.associatedStateKeys) {
      const config = AGENT_CONFIGS[agentId];
      const keysToReset = config?.associatedStateKeys;

      if (keysToReset && Array.isArray(keysToReset)) {
        const newState = { ...get().agentState };
        keysToReset.forEach(key => {
          if (key in newState) {
            delete newState[key];
          }
        });
        set({ agentState: newState });
      } else {
        // Fallback to full reset if keys are not properly defined
        set({ agentState: initialStoreState.agentState });
      }
    } else {
      // Reset all if no specific agentId or no keys defined for it
      set({ agentState: initialStoreState.agentState });
    }
  },

  // Connection State actions
  setIsConnected: (isConnected: boolean) => {
    const state = get();

    set({
      connectionState: {
        ...state.connectionState,
        isConnected,
        ...(isConnected ? { error: null } : {}),
      },
    });
  },
  setConnectionError: (error: string | null) => {
    const state = get();

    set({
      connectionState: {
        ...state.connectionState,
        error,
      },
    });
  },
  incrementReconnectCount: () => {
    const state = get();

    set({
      connectionState: {
        ...state.connectionState,
        reconnectCount: state.connectionState.reconnectCount + 1,
      },
    });
  },
  resetConnectionState: () => set({ connectionState: initialStoreState.connectionState }),

  // Event History actions
  addEvent: (sseEvent: SSEEvent) => {
    let determinedType: EventType;
    const firstPart = sseEvent.content?.parts?.[0] || sseEvent.content?.role;

    if (sseEvent.is_error) {
      determinedType = EventType.ERROR;
    } else if (
      firstPart?.function_call ||
      (sseEvent.content?.role === "model" && firstPart && "functionCall" in firstPart)
    ) {
      determinedType = EventType.TOOL_CALL;
    } else if (
      firstPart?.function_response ||
      (sseEvent.content?.role === "user" && firstPart && "functionResponse" in firstPart)
    ) {
      determinedType = EventType.TOOL_RESPONSE;
    } else if (sseEvent.content?.role === "user") {
      determinedType = EventType.USER_INPUT;
      // If this is a user input, check if it's the first one to save to session history
      const currentEventHistory = get().eventHistory; // Get current state before adding this event

      if (currentEventHistory.length === 0 && firstPart?.text) {
        const { sessionId } = get(); // Get the current sessionId from store

        addSessionToHistory(firstPart.text, sessionId || undefined);
      }
    } else if (
      sseEvent.content?.role === "model" &&
      (firstPart?.text || (firstPart && "text" in firstPart))
    ) {
      determinedType = EventType.AGENT_RESPONSE;
    } else {
      // Fallback to SSE event's type or default to SYSTEM
      determinedType = (sseEvent.type as EventType) || EventType.SYSTEM;
    }

    // Extract tool name from functionCall or function_call
    let toolName = null;
    let toolArgs = null;
    let toolResponse: EventFunctionResponse | undefined = undefined;

    if (firstPart) {
      if (firstPart.function_call) {
        toolName = firstPart.function_call.name;
        toolArgs = firstPart.function_call.args;
      } else if ("functionCall" in firstPart) {
        toolName = (firstPart as any).functionCall.name;
        toolArgs = (firstPart as any).functionCall.args;
      }

      if (firstPart.function_response) {
        // Create a valid EventFunctionResponse object
        toolResponse = {
          id: firstPart.function_response.id,
          name: firstPart.function_response.name,
          response: firstPart.function_response.response || {},
          status: "success",
          // Keep only a summary of the response, not the full content
          summary: `Response from ${firstPart.function_response.name}`,
        };
      } else if ("functionResponse" in firstPart) {
        // Create a valid EventFunctionResponse object
        toolResponse = {
          id: (firstPart as any).functionResponse.id || "",
          name: (firstPart as any).functionResponse.name,
          response: (firstPart as any).functionResponse.response || {},
          status: "success",
          summary: `Response from ${(firstPart as any).functionResponse.name}`,
        };
      }
    }

    const newEvent: EventHistoryItem = {
      id: sseEvent.id,
      timestamp: sseEvent.timestamp,
      type: determinedType,
      author: sseEvent.author,
      displayTimestamp: formatTimestamp(
        sseEvent.timestampMs ??
          (typeof sseEvent.timestamp === "number"
            ? sseEvent.timestamp
            : new Date(sseEvent.timestamp).getTime()),
      ),
      invocation_id: sseEvent.invocation_id,
      content: sseEvent.content,
      tool_call:
        firstPart?.function_call ||
        (firstPart && "functionCall" in firstPart ? (firstPart as any).functionCall : null),
      tool_response: toolResponse,
      tool_name: toolName, // Add explicit tool name field for easier access
      is_error: sseEvent.is_error || false,
      is_final_response: sseEvent.is_final_response || false,
      actions: sseEvent.actions,
      parent_id: sseEvent.parent_id,
      usageMetadata: sseEvent.usageMetadata, // Copy usage metadata for token tracking
    };

    const getNumericTimestamp = (event: EventHistoryItem): number => {
      return (
        event.timestampMs ??
        (typeof event.timestamp === "number"
          ? event.timestamp
          : new Date(event.timestamp).getTime())
      );
    };

    set((state: AgentStore) => ({
      eventHistory: [...state.eventHistory, newEvent].sort(
        (a, b) => getNumericTimestamp(a) - getNumericTimestamp(b),
      ),
    }));
  },

  clearEventHistory: () => set({ eventHistory: initialStoreState.eventHistory }),

  filterEvents: (filters: {
    author?: string;
    type?: EventType;
    toolName?: string;
    isError?: boolean;
  }) => {
    const { eventHistory } = get();

    return eventHistory.filter((event: EventHistoryItem) => {
      if (filters.author && event.author !== filters.author) {
        return false;
      }
      if (filters.type !== undefined && event.type !== filters.type) {
        return false;
      }
      if (filters.toolName !== undefined) {
        // Check if this is a tool call with the specified name
        const isToolCall = event.tool_call && event.tool_call.name === filters.toolName;
        // Check if this is a tool response with the specified name
        const isToolResponse = event.tool_response && event.tool_response.name === filters.toolName;

        if (!isToolCall && !isToolResponse) {
          return false;
        }
      }

      if (filters.isError !== undefined && event.is_error !== filters.isError) {
        return false;
      }

      return true;
    });
  },

  // Get events by specific event type
  getEventsByType: (type: EventType) => {
    const state = get();

    return state.filterEvents({ type });
  },

  // Get all tool call events
  getToolCalls: () => {
    const { eventHistory } = get();

    return eventHistory.filter((event: EventHistoryItem) => event.type === EventType.TOOL_CALL);
  },

  // Get all tool response events
  getToolResponses: () => {
    const { eventHistory } = get();

    return eventHistory.filter((event: EventHistoryItem) => event.type === EventType.TOOL_RESPONSE);
  },

  // Phase 3: Enhanced filtering and navigation functions
  setFilterType: (type: string) => {
    set({ filterType: type });
  },

  setFilterAgent: (agent: AgentName | "all") => { // Updated type
    set({ filterAgent: agent });
  },

  setFilterToolName: (tool: string) => {
    set({ filterToolName: tool });
  },

  setShowOnlyErrors: (show: boolean) => {
    set({ showOnlyErrors: show });
  },

  setUseRelativeTime: (use: boolean) => {
    set({ useRelativeTime: use });
  },

  scrollToEvent: (eventId: string) => {
    // Find the DOM element with the event ID and scroll to it
    const element = document.getElementById(`event-${eventId}`);

    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "center" });

      // Highlight the element briefly
      element.classList.add("highlight-pulse");
      setTimeout(() => {
        element.classList.remove("highlight-pulse");
      }, 2000);
    }
  },

  // Get filtered events based on current filters
  getFilteredEvents: () => {
    const { eventHistory, filterType, filterAgent, filterToolName, showOnlyErrors } = get();

    return eventHistory.filter((event: EventHistoryItem) => {
      // Filter by event type
      if (filterType !== "all") {
        // Handle EventType enum values (numeric strings)
        if (/^\d+$/.test(filterType)) {
          const typeNumber = parseInt(filterType, 10);

          // Only compare if event.type is defined and conversion succeeded
          if (!isNaN(typeNumber) && event.type !== undefined && Number(event.type) !== typeNumber) {
            return false;
          }
        }

        // Handle special filters
        if (filterType === "error" && !event.is_error) return false;
      }

      // Filter by agent
      if (filterAgent !== "all" && event.author !== filterAgent) {
        return false;
      }

      // Filter by tool name
      if (filterToolName !== "all") {
        const toolName = event.tool_call?.name || event.tool_response?.name;

        if (toolName !== filterToolName) return false;
      }

      // Filter errors only
      if (showOnlyErrors && !event.is_error) {
        return false;
      }

      return true;
    });
  },

  // Agent Tracking actions
  setActiveAgent: (agent: AgentName | null) => set({ activeAgent: agent }), // Updated type

  isAgentActive: (agentName: AgentName) => { // Updated type
    if (get().activeAgent === agentName) {
      return true;
    }

    return isAgentActive(agentName, get().eventHistory);
  },

  getAgentStatus: (agentName: AgentName) => { // Updated type
    const state = get();

    // Validate agentName
    if (!agentName || typeof agentName !== 'string') {
      return "idle";
    }

    const agentEvents = state.eventHistory.filter(
      (event: EventHistoryItem) => event.author === agentName,
    );

    // Check for errors
    if (agentEvents.some((event: EventHistoryItem) => event.is_error)) {
      return "error";
    }

    // Check if active
    if (state.activeAgent === agentName) {
      return "active";
    }

    // Enhanced completion detection - check for output data in agentState
    const agentConfig = AGENT_CONFIGS[agentName];
    const outputKeys = agentConfig?.associatedStateKeys;

    if (outputKeys) {
      for (const outputKey of outputKeys) {
        if (state.agentState[outputKey]) {
          const outputData = state.agentState[outputKey];

          // Check if the agent has produced meaningful output
          let hasOutput = false;

          if (typeof outputData === "string") {
            hasOutput = outputData.trim().length > 0;
          } else if (typeof outputData === "object" && outputData !== null) {
            const keys = Object.keys(outputData);

            hasOutput =
              keys.length > 0 &&
              keys.some((key) => {
                const value = outputData[key];

                return (
                  value !== null &&
                  value !== undefined &&
                  (typeof value !== "string" || value.trim().length > 0)
                );
              });
          }

          if (hasOutput) {
            return "completed";
          }
        }
      }
    }

    // Check for completed using is_final_response as fallback
    if (agentEvents.some((event: EventHistoryItem) => event.is_final_response)) {
      return "completed";
    }

    // Default to idle
    return "idle";
  },

  // Active Tab actions
  setActiveTab: (tab: TabKey) => set({ activeTab: tab }),

  // Session ID actions
  initializeSessionId: async (): Promise<{ userId: string; sessionId: string }> => {
    const callInstanceId = `initCall-${Math.random().toString(36).substring(2, 7)}`;

    if (sessionInitializationPromise) {
      return sessionInitializationPromise; // Return existing promise if one is already in flight or resolved
    }

    // No existing promise, so create a new one.
    const currentPromiseInstanceId = ++promiseCreationCounter;

    sessionInitializationPromise = (async () => {
      try {
        // Check if session ID (which should be server-ID) already exists from a previous successful run.
        // This check is inside the IIFE to ensure it's part of the promise's logic.
        const currentState = get();

        if (currentState.sessionId && currentState.userId) {
          return { userId: currentState.userId, sessionId: currentState.sessionId };
        }

        // Mark that an attempt is now definitely in progress.
        // isSessionInitializationAttempted is useful for UI to know an attempt has started/finished.
        if (!currentState.isSessionInitializationAttempted) {
          set({ isSessionInitializationAttempted: true });
        }

        const timestamp = Date.now();
        const clientUserId = `user-${timestamp}`;
        const clientSessionId = `session-${timestamp}`;

        const {
          appName,
          userId: currentUserIdFromStore,
          sessionId: currentSessionIdFromStore,
        } = get();

        if (!appName) {
          console.error(
            `[${callInstanceId}-${currentPromiseInstanceId}] App name not set, cannot initialize session.`,
          );
          throw new Error("App name not set, cannot initialize session.");
        }

        let userIdToUse = currentUserIdFromStore;
        let sessionIdToUse = currentSessionIdFromStore;
        const uniqueIdSuffix = () => Math.random().toString(36).substring(2, 9);

        if (!userIdToUse) {
          let storedUserId =
            typeof window !== "undefined" ? localStorage.getItem("adk_userId") : null;

          if (!storedUserId) {
            storedUserId = `user-${Date.now().toString(36)}-${uniqueIdSuffix()}`;
            if (typeof window !== "undefined") {
              localStorage.setItem("adk_userId", storedUserId);
            }
          }
          userIdToUse = storedUserId;
        }

        if (!sessionIdToUse) {
          sessionIdToUse = `session-${Date.now().toString(36)}-${uniqueIdSuffix()}`;
        }

        // Call the refactored sessionService.initializeSession
        const { userId: finalizedUserId, sessionId: finalizedSessionId } =
          await sessionService.initializeSession(appName, userIdToUse, sessionIdToUse);

        set({
          userId: finalizedUserId,
          sessionId: finalizedSessionId,
          isSessionInitialized: true,
          isSessionInitializationAttempted: true,
          sessionInitializationError: null,
        });

        return { userId: finalizedUserId, sessionId: finalizedSessionId };
      } catch (error) {
        console.error(
          `[PromiseInstanceID: ${currentPromiseInstanceId}] Error during session initialization:`,
          error,
        );
        // Ensure state reflects failed attempt if not already set by a more specific error
        set({ sessionId: null, userId: null, isSessionInitializationAttempted: true });
        // Do NOT set sessionInitializationPromise = null here. Let the promise reject.
        if (error instanceof Error) {
          throw error; // Re-throw the original error or a new one wrapping it
        } else {
          throw new Error(`Unknown error during session initialization: ${String(error)}`);
        }
      }
    })();

    return sessionInitializationPromise;
  },
  setSessionId: (sessionId: string | null) => set({ sessionId }),
  setUserId: (userId: string | null) => set({ userId }),

  // App Name actions
  setAppName: (appName: string | null) => set({ appName }),
  setAvailableApps: (availableApps: string[] | null) => set({ availableApps }),

  // Agent Graph Data actions
  fetchAgentGraph: async (eventId: string) => {
    const store = get(); // Get the whole store once
    const { appName, userId: initialUserId, sessionId: initialSessionId, isSessionInitializationAttempted, initializeSessionId } =
      store;
    
    let userId = initialUserId;
    let sessionId = initialSessionId;

    if (!appName) {
      set({ graphFetchError: "Application name is not configured.", isFetchingGraph: false });

      return;
    }

    set({ isFetchingGraph: true, graphFetchError: null });

    try {
      // Check if session is already initialized and valid in the store
      if (!isSessionInitializationAttempted || !userId || !sessionId) {
        const sessionDetails = await initializeSessionId(); // This will use the debouncing logic

        userId = sessionDetails.userId;
        sessionId = sessionDetails.sessionId;
      }

      if (!userId || !sessionId) {
        throw new Error("Session not properly initialized for graph fetching.");
      }

      const graphUrl = `${NEXT_PUBLIC_BACKEND_URL}/apps/${appName}/users/${userId}/sessions/${sessionId}/events/${eventId}/graph`;

      const response = await fetch(graphUrl);

      if (!response.ok) {
        const errorText = await response
          .text()
          .catch(() => `HTTP error ${response.status} with no error text.`);

        throw new Error(`Failed to fetch agent graph: ${response.status} - ${errorText}`);
      }

      const graphData = await response.json();

      if (graphData && typeof graphData.dotSrc === "string") {
        set({
          agentGraphDotSource: graphData.dotSrc,
          isFetchingGraph: false,
          graphFetchError: null,
        });
      } else {
        console.warn(
          "[fetchAgentGraph] Fetched graph data does not contain a 'dotSrc' string property:",
          graphData,
        );
        set({
          agentGraphDotSource: null,
          isFetchingGraph: false,
          graphFetchError: "Received invalid graph data format from server.",
        });
      }
    } catch (error) {
      console.error("[fetchAgentGraph] Error fetching or processing agent graph:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);

      set({ agentGraphDotSource: null, isFetchingGraph: false, graphFetchError: errorMessage });
    } // End of catch block, also ends the try block
  }, // End of fetchAgentGraph method
  fetchAndSetAppName: async () => {
    const invocationId = Math.random().toString(36).substring(2, 7); // Unique ID for this invocation
    const currentStoreState = get();

    if (currentStoreState.isAppNameInitialized) {
      return;
    }

    if (appNameFetchPromise) {
      return appNameFetchPromise;
    }

    const fetchLogic = async () => {
      try {
        const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

        if (!backendUrl) {
          set((state) => ({
            ...state,
            appName: null,
            availableApps: [],
            connectionState: {
              ...state.connectionState,
              error: "Backend URL not configured.",
            },
            isAppNameInitialized: true, // Mark initialized to prevent retries for this specific error
          }));

          return;
        }
        const response = await fetch(`${backendUrl}/list-apps`);

        if (!response.ok) {
          throw new Error(`Failed to fetch app name/list: ${response.status}`);
        }
        const data = await response.json();
        const appList = Array.isArray(data) ? data : [];

        set((state) => ({
          ...state,
          appName: appList.length > 0 ? appList[0] : null,
          availableApps: appList,
          isAppNameInitialized: true,
        }));
      } catch (err) {
        console.error(`[${invocationId}] fetchLogic ERROR:`, err);
        set((state) => ({
          ...state,
          availableApps: [],
          appName: null,
          connectionState: {
            ...state.connectionState,
            error: `Failed to fetch app list: ${err instanceof Error ? err.message : String(err)}`,
          },
          isAppNameInitialized: true, // Mark initialized even on error to prevent hammering
        }));
      } finally {
        appNameFetchPromise = null;
      }
    };

    appNameFetchPromise = fetchLogic();

    return appNameFetchPromise;
  },
}));

export const agentStoreApi: StoreApi<AgentStore> = useAgentStore;
