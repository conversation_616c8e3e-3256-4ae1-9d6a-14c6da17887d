// app/agent/services/__tests__/pricingService.test.ts

import { 
  calculateDynamicCost, 
  findModelPricing, 
  fetchLiteLLMPricing 
} from '../pricingService';

// Mock fetch for testing
global.fetch = jest.fn();

describe('PricingService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchLiteLLMPricing', () => {
    it('should fetch pricing data successfully', async () => {
      const mockPricingData = {
        'gemini-2.5-pro': {
          input_cost_per_million_tokens: 1.25,
          output_cost_per_million_tokens: 10.0,
          max_input_tokens: 200000,
        },
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockPricingData,
      });

      const result = await fetchLiteLLMPricing();
      expect(result).toEqual(mockPricingData);
      expect(fetch).toHaveBeenCalledWith(
        'https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Accept': 'application/json',
            'Cache-Control': 'no-cache',
          }),
        })
      );
    });

    it('should return null on fetch failure', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      const result = await fetchLiteLLMPricing();
      expect(result).toBeNull();
    });

    it('should return null on non-ok response', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
      });

      const result = await fetchLiteLLMPricing();
      expect(result).toBeNull();
    });
  });

  describe('findModelPricing', () => {
    const mockPricingData = {
      'gemini-2.5-pro': {
        input_cost_per_million_tokens: 1.25,
        output_cost_per_million_tokens: 10.0,
      },
      'gpt-4': {
        input_cost_per_million_tokens: 30.0,
        output_cost_per_million_tokens: 60.0,
      },
      'claude-3-opus': {
        input_cost_per_million_tokens: 15.0,
        output_cost_per_million_tokens: 75.0,
      },
    };

    it('should find exact model match', () => {
      const result = findModelPricing('gemini-2.5-pro', mockPricingData);
      expect(result).toEqual(mockPricingData['gemini-2.5-pro']);
    });

    it('should find partial model match', () => {
      const result = findModelPricing('gemini', mockPricingData);
      expect(result).toEqual(mockPricingData['gemini-2.5-pro']);
    });

    it('should return null for no match', () => {
      const result = findModelPricing('unknown-model', mockPricingData);
      expect(result).toBeNull();
    });

    it('should handle case insensitive matching', () => {
      const result = findModelPricing('GEMINI-2.5-PRO', mockPricingData);
      expect(result).toEqual(mockPricingData['gemini-2.5-pro']);
    });

    it('should handle different separators', () => {
      const result = findModelPricing('gemini_2_5_pro', mockPricingData);
      expect(result).toEqual(mockPricingData['gemini-2.5-pro']);
    });
  });

  describe('calculateDynamicCost', () => {
    it('should return null for null inputs', async () => {
      const result = await calculateDynamicCost(null, 1000);
      expect(result).toBeNull();

      const result2 = await calculateDynamicCost(1000, null);
      expect(result2).toBeNull();
    });

    it('should use fallback pricing when no model found', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({}), // Empty pricing data
      });

      const result = await calculateDynamicCost(1000, 500, 'unknown-model');
      expect(result).toBeGreaterThan(0);
      expect(typeof result).toBe('number');
    });

    it('should calculate cost correctly with tiered pricing', async () => {
      // Test with tokens under 200k threshold
      const result = await calculateDynamicCost(100000, 50000);
      expect(result).toBeGreaterThan(0);
      expect(typeof result).toBe('number');

      // Test with tokens over 200k threshold
      const result2 = await calculateDynamicCost(300000, 250000);
      expect(result2).toBeGreaterThan(result);
    });

    it('should handle fetch errors gracefully', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      const result = await calculateDynamicCost(1000, 500);
      expect(result).toBeGreaterThan(0);
      expect(typeof result).toBe('number');
    });
  });
});
