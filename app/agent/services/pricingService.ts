// app/agent/services/pricingService.ts

interface ModelPricing {
  input_cost_per_token?: number;
  output_cost_per_token?: number;
  input_cost_per_million_tokens?: number;
  output_cost_per_million_tokens?: number;
  max_tokens?: number;
  max_input_tokens?: number;
  max_output_tokens?: number;
  litellm_provider?: string;
  mode?: string;
}

interface LiteLLMPricingData {
  [modelName: string]: ModelPricing;
}

// Cache for pricing data
let pricingCache: LiteLLMPricingData | null = null;
let lastFetchTime: number = 0;
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Fallback pricing for Gemini 2.5 Pro (current hardcoded values)
const FALLBACK_PRICING: ModelPricing = {
  input_cost_per_million_tokens: 1.25, // $1.25 per 1M tokens for prompts <= 200k
  output_cost_per_million_tokens: 10.0, // $10.00 per 1M tokens for output <= 200k
  max_input_tokens: 200000,
};

// Enhanced fallback pricing with tiered pricing for Gemini 2.5 Pro
const GEMINI_TIERED_PRICING = {
  prompt: {
    under_200k: 1.25, // $1.25 per 1M tokens
    over_200k: 2.5,   // $2.50 per 1M tokens
  },
  output: {
    under_200k: 10.0, // $10.00 per 1M tokens
    over_200k: 15.0,  // $15.00 per 1M tokens
  },
  threshold: 200000,
};

// Comprehensive fallback pricing data for common models
const FALLBACK_PRICING_DATA: LiteLLMPricingData = {
  'gemini-2.5-flash': {
    input_cost_per_million_tokens: 0.075, // $0.075 per 1M tokens for prompts <= 128k
    output_cost_per_million_tokens: 0.30, // $0.30 per 1M tokens for output <= 128k
    max_input_tokens: 128000,
  },
  'gemini-2.5-pro': {
    input_cost_per_million_tokens: 1.25, // $1.25 per 1M tokens for prompts <= 200k
    output_cost_per_million_tokens: 10.0, // $10.00 per 1M tokens for output <= 200k
    max_input_tokens: 200000,
  },
  'gpt-4': {
    input_cost_per_million_tokens: 30.0,
    output_cost_per_million_tokens: 60.0,
    max_input_tokens: 8192,
  },
  'gpt-4-turbo': {
    input_cost_per_million_tokens: 10.0,
    output_cost_per_million_tokens: 30.0,
    max_input_tokens: 128000,
  },
  'gpt-3.5-turbo': {
    input_cost_per_million_tokens: 0.5,
    output_cost_per_million_tokens: 1.5,
    max_input_tokens: 16385,
  },
  'claude-3-opus': {
    input_cost_per_million_tokens: 15.0,
    output_cost_per_million_tokens: 75.0,
    max_input_tokens: 200000,
  },
  'claude-3-sonnet': {
    input_cost_per_million_tokens: 3.0,
    output_cost_per_million_tokens: 15.0,
    max_input_tokens: 200000,
  },
  'claude-3-haiku': {
    input_cost_per_million_tokens: 0.25,
    output_cost_per_million_tokens: 1.25,
    max_input_tokens: 200000,
  },
};

/**
 * Fetches pricing data from LiteLLM repository with multiple fallback strategies
 */
export async function fetchLiteLLMPricing(): Promise<LiteLLMPricingData | null> {
  // Multiple URLs to try in order of preference
  const urls = [
    '/api/pricing', // Server-side proxy (solves CORS/CSP issues)
    'https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json',
    'https://api.github.com/repos/BerriAI/litellm/contents/model_prices_and_context_window.json',
  ];

  for (const url of urls) {
    try {
      console.log(`Attempting to fetch pricing data from: ${url}`);

      // Create abort controller for timeout (fallback for older browsers)
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.warn(`Failed to fetch from ${url}: ${response.status} ${response.statusText}`);
        continue; // Try next URL
      }

      let data: any;

      // Handle different response formats
      if (url.startsWith('/api/pricing')) {
        // Our API route returns JSON directly
        data = await response.json();
        console.log('✅ Successfully fetched from server-side proxy');
      } else if (url.includes('api.github.com')) {
        // GitHub API returns base64 encoded content
        const apiResponse = await response.json();
        if (apiResponse.content && apiResponse.encoding === 'base64') {
          const decodedContent = atob(apiResponse.content);
          data = JSON.parse(decodedContent);
        } else {
          throw new Error('Unexpected GitHub API response format');
        }
      } else {
        // Raw GitHub content
        data = await response.json();
      }

      // Validate the data structure
      if (typeof data !== 'object' || data === null) {
        console.warn(`Invalid pricing data format from ${url}`);
        continue;
      }

      const modelCount = Object.keys(data).length;
      console.info(`✅ Successfully fetched ${modelCount} model prices from: ${url}`);
      return data as LiteLLMPricingData;

    } catch (error) {
      console.warn(`Failed to fetch from ${url}:`, error);

      // Log specific error details for debugging
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.log('Debug: This is likely a CORS, CSP, or network connectivity issue');
      } else if (error instanceof DOMException && error.name === 'AbortError') {
        console.log('Debug: Request was aborted (likely timeout)');
      } else {
        // Safely handle unknown error type
        const errorName = error && typeof error === 'object' && 'constructor' in error
          ? (error.constructor as any)?.name || 'Unknown'
          : 'Unknown';
        const errorMessage = error && typeof error === 'object' && 'message' in error
          ? (error as any).message
          : String(error);
        console.log('Debug: Unexpected error type:', errorName, errorMessage);
      }

      // Continue to next URL
    }
  }

  console.warn('External pricing sources unavailable, using built-in pricing data');
  console.log('Debug: All attempted URLs failed. This could be due to:');
  console.log('1. Network connectivity issues');
  console.log('2. Content Security Policy (CSP) restrictions');
  console.log('3. Browser security policies blocking external requests');
  console.log('4. GitHub API rate limiting');
  console.log('5. Invalid or changed URLs');
  return null;
}

/**
 * Gets cached pricing data or fetches fresh data if cache is expired
 */
export async function getPricingData(): Promise<LiteLLMPricingData> {
  const now = Date.now();

  // Return cached data if it's still valid
  if (pricingCache && (now - lastFetchTime) < CACHE_DURATION) {
    console.log('Using cached pricing data');
    return pricingCache;
  }

  console.log('Cache expired or empty, fetching fresh pricing data...');

  // Try to fetch fresh data
  try {
    const freshData = await fetchLiteLLMPricing();

    if (freshData) {
      pricingCache = freshData;
      lastFetchTime = now;
      console.log('Successfully updated pricing cache');
      return freshData;
    }
  } catch (error) {
    console.error('Error during pricing data fetch:', error);
  }

  // If fetch failed but we have cached data, return it
  if (pricingCache) {
    console.warn('Using stale pricing data due to fetch failure');
    return pricingCache;
  }

  // If no cached data and fetch failed, return fallback pricing data
  console.info('Using built-in pricing data (external sources unavailable)');
  return FALLBACK_PRICING_DATA;
}

/**
 * Finds the best matching model pricing for a given model name
 */
export function findModelPricing(modelName: string, pricingData: LiteLLMPricingData): ModelPricing | null {
  // Direct match
  if (pricingData[modelName]) {
    return pricingData[modelName];
  }

  // Try common variations and partial matches
  const normalizedModelName = modelName.toLowerCase().replace(/[-_\s]/g, '');
  
  for (const [key, pricing] of Object.entries(pricingData)) {
    const normalizedKey = key.toLowerCase().replace(/[-_\s]/g, '');
    
    // Check for partial matches
    if (normalizedKey.includes(normalizedModelName) || normalizedModelName.includes(normalizedKey)) {
      return pricing;
    }
  }

  return null;
}

/**
 * Calculates cost using dynamic pricing with fallback to hardcoded values
 */
export async function calculateDynamicCost(
  promptTokens: number | null,
  completionTokens: number | null,
  modelName: string = 'gemini-2.5-flash'
): Promise<number | null> {
  if (promptTokens === null || completionTokens === null) {
    return null;
  }

  try {
    const pricingData = await getPricingData();
    const modelPricing = findModelPricing(modelName, pricingData);

    if (modelPricing) {
      return calculateCostFromPricing(promptTokens, completionTokens, modelPricing);
    }

    // Fallback to hardcoded Gemini pricing with tiered structure
    console.info(`Using built-in pricing for ${modelName} (model not found in external data)`);
    return calculateGeminiTieredCost(promptTokens, completionTokens);

  } catch (error) {
    console.error('Error calculating dynamic cost:', error);
    // Fallback to hardcoded pricing
    return calculateGeminiTieredCost(promptTokens, completionTokens);
  }
}

/**
 * Calculates cost from LiteLLM pricing structure
 */
function calculateCostFromPricing(
  promptTokens: number,
  completionTokens: number,
  pricing: ModelPricing
): number {
  let promptCost = 0;
  let completionCost = 0;

  // Handle different pricing formats
  if (pricing.input_cost_per_token) {
    promptCost = promptTokens * pricing.input_cost_per_token;
  } else if (pricing.input_cost_per_million_tokens) {
    promptCost = (promptTokens / 1_000_000) * pricing.input_cost_per_million_tokens;
  }

  if (pricing.output_cost_per_token) {
    completionCost = completionTokens * pricing.output_cost_per_token;
  } else if (pricing.output_cost_per_million_tokens) {
    completionCost = (completionTokens / 1_000_000) * pricing.output_cost_per_million_tokens;
  }

  return promptCost + completionCost;
}

/**
 * Calculates cost using Gemini's tiered pricing structure
 */
function calculateGeminiTieredCost(promptTokens: number, completionTokens: number): number {
  const { prompt, output, threshold } = GEMINI_TIERED_PRICING;
  const TOKENS_PER_MILLION = 1_000_000;

  // Calculate prompt cost with tiered pricing
  let promptCost = 0;
  if (promptTokens <= threshold) {
    promptCost = (promptTokens / TOKENS_PER_MILLION) * prompt.under_200k;
  } else {
    const under200k = threshold;
    const over200k = promptTokens - threshold;
    promptCost = 
      (under200k / TOKENS_PER_MILLION) * prompt.under_200k +
      (over200k / TOKENS_PER_MILLION) * prompt.over_200k;
  }

  // Calculate completion cost with tiered pricing
  let completionCost = 0;
  if (completionTokens <= threshold) {
    completionCost = (completionTokens / TOKENS_PER_MILLION) * output.under_200k;
  } else {
    const under200k = threshold;
    const over200k = completionTokens - threshold;
    completionCost = 
      (under200k / TOKENS_PER_MILLION) * output.under_200k +
      (over200k / TOKENS_PER_MILLION) * output.over_200k;
  }

  return promptCost + completionCost;
}

/**
 * Gets available models from pricing data
 */
export async function getAvailableModels(): Promise<string[]> {
  try {
    const pricingData = await getPricingData();
    return Object.keys(pricingData).sort();
  } catch (error) {
    console.error('Error getting available models:', error);
    return ['gemini-2.5-flash', 'gemini-2.5-pro']; // Fallback
  }
}

/**
 * Preloads pricing data for better performance
 */
export async function preloadPricingData(): Promise<void> {
  try {
    console.log('Starting pricing data preload...');
    const data = await getPricingData();
    const modelCount = Object.keys(data).length;
    console.log(`Pricing data preloaded successfully with ${modelCount} models`);
  } catch (error) {
    const errorMessage = error && typeof error === 'object' && 'message' in error
      ? (error as any).message
      : String(error);
    console.info('External pricing sources unavailable, built-in pricing ready:', errorMessage);
    // Don't throw the error, just log it - the app should continue working
  }
}
