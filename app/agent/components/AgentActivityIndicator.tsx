"use client";

import { Chip } from "@heroui/chip";
import { useEffect, useState, useMemo } from "react";

import { useAgentStore } from "../store/agentStore";
import { EventType, AgentName } from "../types";

interface AgentActivity {
  agentName: string;
  displayName: string;
  activity: string;
  icon: string;
  color: string;
  lastSeen: number;
}

const AGENT_DISPLAY_NAMES: Record<string, string> = {
  'ReportSynthesizerAgent': 'Report Synthesizer',
  'FinancialIntelligenceAgent': 'Financial Intelligence',
  'RiskIntelligenceAgent': 'Risk Intelligence',
  'ComplianceIntelligenceAgent': 'Compliance Intelligence',
  'LegalIntelligenceAgent': 'Legal Intelligence',
  'EntityIntelligenceAgent': 'Entity Intelligence',
  'DueDiligenceOrchestrator': 'Due Diligence Orchestrator',
  'ParallelAgent': 'Parallel Orchestrator',
};

const AGENT_ICONS: Record<string, string> = {
  'ReportSynthesizerAgent': '📊',
  'FinancialIntelligenceAgent': '💰',
  'RiskIntelligenceAgent': '⚠️',
  'ComplianceIntelligenceAgent': '📋',
  'LegalIntelligenceAgent': '⚖️',
  'EntityIntelligenceAgent': '🏢',
  'DueDiligenceOrchestrator': '🎯',
  'ParallelAgent': '🔄',
};

const ACTIVITY_DESCRIPTIONS: Record<number, string> = {
  [EventType.TOOL_CALL]: 'Using tools',
  [EventType.TOOL_RESPONSE]: 'Processing results',
  [EventType.AGENT_RESPONSE]: 'Generating response',
  [EventType.USER_INPUT]: 'Received input',
};

export default function AgentActivityIndicator() {
  const eventHistory = useAgentStore((state) => state.eventHistory);
  const connectionState = useAgentStore((state) => state.connectionState);
  const [currentTime, setCurrentTime] = useState(Date.now());

  // Update current time every second for activity timeout
  useEffect(() => {
    const interval = setInterval(() => setCurrentTime(Date.now()), 1000);
    return () => clearInterval(interval);
  }, []);

  // Calculate active agents based on recent activity
  const activeAgents = useMemo(() => {
    const now = currentTime;
    const ACTIVITY_TIMEOUT = 10000; // 10 seconds
    const activities: AgentActivity[] = [];

    // Get recent events (last 30 seconds)
    const recentEvents = eventHistory.filter(event => {
      const eventTime = event.timestampMs || new Date(event.timestamp || 0).getTime();
      return (now - eventTime) < 30000; // 30 seconds
    });

    // Group by agent and find latest activity
    const agentActivities = new Map<string, { lastEvent: any; lastSeen: number }>();

    recentEvents.forEach(event => {
      if (event.author && event.author !== 'user' && event.author !== 'system') {
        const eventTime = event.timestampMs || new Date(event.timestamp || 0).getTime();
        const existing = agentActivities.get(event.author);
        
        if (!existing || eventTime > existing.lastSeen) {
          agentActivities.set(event.author, {
            lastEvent: event,
            lastSeen: eventTime
          });
        }
      }
    });

    // Convert to activity objects for agents active within timeout
    agentActivities.forEach(({ lastEvent, lastSeen }, agentName) => {
      if ((now - lastSeen) < ACTIVITY_TIMEOUT) {
        const displayName = AGENT_DISPLAY_NAMES[agentName] || agentName;
        const icon = AGENT_ICONS[agentName] || '🤖';
        const activity = ACTIVITY_DESCRIPTIONS[lastEvent.type] || 'Working';
        
        // Determine color based on activity type and recency
        let color = 'primary';
        if (lastEvent.type === EventType.TOOL_CALL) color = 'warning';
        else if (lastEvent.type === EventType.TOOL_RESPONSE) color = 'success';
        else if (lastEvent.is_error) color = 'danger';

        activities.push({
          agentName,
          displayName,
          activity,
          icon,
          color,
          lastSeen
        });
      }
    });

    // Sort by most recent activity
    return activities.sort((a, b) => b.lastSeen - a.lastSeen);
  }, [eventHistory, currentTime]);

  // Don't show if no active agents or not connected
  if (activeAgents.length === 0 && !connectionState.isConnected) {
    return null;
  }

  return (
    <div className="border-t border-divider/30 bg-content1/50 backdrop-blur-sm">
      <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Connection status */}
            {connectionState.isConnected && (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse" />
                <span className="text-xs text-foreground-600 font-medium">
                  Agents Active
                </span>
              </div>
            )}
            
            {/* Active agents */}
            <div className="flex items-center gap-2 flex-wrap">
              {activeAgents.slice(0, 3).map((agent) => {
                const timeSinceActivity = Math.floor((currentTime - agent.lastSeen) / 1000);
                
                return (
                  <Chip
                    key={agent.agentName}
                    color={agent.color as any}
                    size="sm"
                    variant="flat"
                    className="text-xs animate-in fade-in-0 duration-300"
                  >
                    <div className="flex items-center gap-1">
                      <span>{agent.icon}</span>
                      <span className="font-medium">{agent.displayName}</span>
                      <span className="text-foreground-500">•</span>
                      <span>{agent.activity}</span>
                      {timeSinceActivity < 5 && (
                        <div className="w-1 h-1 rounded-full bg-current animate-pulse ml-1" />
                      )}
                    </div>
                  </Chip>
                );
              })}
              
              {/* Show count if more agents */}
              {activeAgents.length > 3 && (
                <Chip
                  color="default"
                  size="sm"
                  variant="bordered"
                  className="text-xs"
                >
                  +{activeAgents.length - 3} more
                </Chip>
              )}
            </div>
          </div>

          {/* Quick stats */}
          {activeAgents.length > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-xs text-foreground-500">
                {activeAgents.length} agent{activeAgents.length !== 1 ? 's' : ''} working
              </span>
            </div>
          )}
        </div>

        {/* Recent activity summary for connected state */}
        {connectionState.isConnected && activeAgents.length === 0 && (
          <div className="flex items-center gap-2 mt-1">
            <div className="w-2 h-2 rounded-full bg-blue-400 animate-pulse" />
            <span className="text-xs text-foreground-500">
              Waiting for agent response...
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
