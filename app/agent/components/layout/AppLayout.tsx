"use client";

import React, { ReactNode } from "react";

import ErrorBoundary from "../ErrorBoundary";
import MainContentArea from "./MainContentArea";
import NavigationHeader from "./NavigationHeader";
import PixelatedBackground from "./PixelatedBackground";
import Sidebar from "./Sidebar";

interface AppLayoutProps {
  children: ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  return (
    <ErrorBoundary>
      <div className="relative flex flex-col h-full overflow-hidden">
        <PixelatedBackground />
        <div className="relative z-10 flex flex-col h-full overflow-hidden">
          <ErrorBoundary>
            <NavigationHeader />
          </ErrorBoundary>
          <div className="flex flex-1 overflow-hidden">
            <ErrorBoundary>
              <Sidebar />
            </ErrorBoundary>
            <ErrorBoundary>
              <MainContentArea>{children}</MainContentArea>
            </ErrorBoundary>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default AppLayout;
