"use client";

import type { AgentStore } from "../../store/agentStore"; // Keep for selector types

import { <PERSON><PERSON> } from "@heroui/button";
import { Tab, Tabs } from "@heroui/tabs";
import { useTheme } from "next-themes";
import React, { useEffect, useState } from "react";

import { agent<PERSON>tore<PERSON><PERSON>, useAgentStore } from "../../store/agentStore"; // Use both for clarity
import { TabKey } from "../../types";
import ExampleQueriesDisplay from "../ExampleQueriesDisplay";
import { preloadPricingData } from "../../services/pricingService";
// Demo mode has been removed

// Note: Theme toggle removed to avoid duplication with navbar theme switch

const NavigationHeader: React.FC = () => {
  const [isExampleQueriesOpen, setIsExampleQueriesOpen] = useState(false);

  const exampleQueriesData = [
    "## 1. Full Due Diligence Study\n\n```text\nConduct a comprehensive due diligence assessment for Nebius AI Cloud. Include financial analysis, risk assessment, compliance checks, legal review, and a summary report with key insights.\n```\n\n---\n\n## 2. Data Collection\n\n```text\nGather all publicly available information about GreenLeaf Biotech, including recent news, financial statements, and regulatory filings.\n```\n\n---\n\n## 3. Financial Analysis\n\n```text\nAnalyze the financial health of BlueWave Logistics for the past three years. Highlight any red flags or unusual trends in their balance sheets and cash flow statements.\n```\n\n---\n\n## 4. Risk Assessment\n\n```text\nIdentify and assess the top five operational and market risks facing SolarEdge Energy in 2024.\n```\n\n---\n\n## 5. Compliance Check\n\n```text\nCheck if MedicaPharm Ltd. is compliant with US and EU anti-money laundering (AML) regulations and identify any recent compliance violations.\n```\n\n---\n\n## 6. Legal Review\n\n```text\nReview the recent legal cases and intellectual property disputes involving QuantumAI Solutions.\n```\n\n---\n\n## 7. Entity Mapping\n\n```text\nMap the ownership structure and key stakeholders of Oceanic Ventures, including subsidiaries and major investors.\n```\n\n---\n\n## 8. Report Insights\n\n```text\nSummarize the key findings from the due diligence report on RedPine Technologies, focusing on financial stability, compliance status, and legal risks.\n```\n\n---\n\n## 9. Multi-Aspect Query\n\n```text\nFor Silverline Holdings, provide a due diligence summary covering financials, compliance, legal issues, and risk factors relevant to a potential merger.\n```\n\n---\n\n## 10. Custom/Ad Hoc Query\n\n```text\nWhat are the most recent regulatory changes affecting fintech companies in Singapore, and how might they impact due diligence requirements for new market entrants?\n```",
  ];

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Preload pricing data in parallel with app initialization
        preloadPricingData().catch(error =>
          console.warn("Failed to preload pricing data:", error)
        );

        // First, fetch and set the app name
        await agentStoreApi.getState().fetchAndSetAppName();

        // Check if appName was successfully set
        const state = agentStoreApi.getState();

        if (state.appName) {
          // If appName is available, initialize the session
          await state.initializeSessionId();
        } else {
          console.warn(
            "App name not available after fetchAndSetAppName. Session initialization skipped.",
          );
          // Add retry mechanism after a delay
          setTimeout(async () => {
            const currentState = agentStoreApi.getState();

            if (!currentState.sessionId && currentState.appName) {
              await currentState.initializeSessionId();
            }
          }, 1000); // Retry after 1 second
        }
      } catch (error) {
        console.error("Error during app initialization:", error);
      }
    };

    initializeApp();
  }, []); // Empty dependency array ensures this runs only once on mount

  // Select state and actions individually
  const sessionId = useAgentStore((state: AgentStore) => state.sessionId);
  const activeTab = useAgentStore((state: AgentStore) => state.activeTab);
  const setActiveTab = useAgentStore((state: AgentStore) => state.setActiveTab);
  const isConnected = useAgentStore((state: AgentStore) => state.connectionState.isConnected);
  const resetAgentState = useAgentStore((state: AgentStore) => state.resetAgentState);
  const clearEventHistory = useAgentStore((state: AgentStore) => state.clearEventHistory);

  const tabs: { key: TabKey; label: string }[] = [
    { key: "events", label: "Events" },
    { key: "state", label: "State" },
    { key: "trace", label: "Trace" },
    { key: "sessions", label: "Sessions" },
    { key: "eval", label: "Eval" },
  ];

  const handleNewSession = () => {
    resetAgentState();
    clearEventHistory();
  };

  return (
    <>
      <header className="glass-panel-dark border-b border-white/10 z-20 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-cyan-500/5 opacity-50" />
        <div className="relative flex items-center justify-between px-6 py-4">
          {/* Left side: Title and Session Info */}
          <div className="flex items-center space-x-6">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
              ADK Frontend
            </h1>

            {/* Session ID and Connection Status */}
            <div className="glass-panel flex items-center px-4 py-2 rounded-lg">
              <div className="flex flex-col mr-4">
                <span className="text-xs text-foreground-500 uppercase tracking-wider">Session ID</span>
                <span className="text-sm font-mono text-foreground-300">{sessionId || "loading..."}</span>
              </div>

              <div className="flex items-center border-l border-white/20 pl-4">
                {isConnected && (
                  <div className="flex items-center mr-3">
                    <span className="inline-block w-2 h-2 rounded-full bg-yellow-400 animate-pulse mr-1.5 shadow-[0_0_8px_rgba(250,204,21,0.5)]" />
                    <span className="text-xs text-foreground-400 uppercase tracking-wider">Token Stream</span>
                  </div>
                )}
                <span
                  className={`inline-block w-2.5 h-2.5 rounded-full mr-1.5 ${
                    isConnected
                      ? "bg-green-400 shadow-[0_0_10px_rgba(74,222,128,0.5)]"
                      : "bg-red-400 shadow-[0_0_10px_rgba(248,113,113,0.5)]"
                  }`}
                />
                <span className="text-xs text-foreground-300 font-medium">
                  {isConnected ? "Connected" : "Disconnected"}
                </span>
              </div>
            </div>
          </div>

          {/* Right side: Action buttons and Tabs */}
          <div className="flex items-center space-x-2">
            {/* Action Buttons */}
            <Button
              className="glass-panel flex items-center hover:scale-105 transition-all duration-300 px-4 py-2 rounded-lg border border-white/20 hover:border-white/30 bg-gradient-to-r from-blue-500/10 to-purple-500/10 hover:from-blue-500/20 hover:to-purple-500/20"
              size="sm"
              onClick={handleNewSession}
            >
              <svg
                className="mr-1"
                fill="none"
                height="16"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M12 5v14M5 12h14" />
              </svg>
              New Session
            </Button>

            <Button
              isIconOnly
              aria-label="Clear session"
              className="hover:bg-red-500/20 hover:text-red-400 transition-all hover:scale-110"
              size="sm"
              variant="light"
              onClick={handleNewSession}
            >
              <svg
                fill="none"
                height="16"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M3 6h18M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
              </svg>
            </Button>

            {/* Example Queries Button */}
            <Button
              aria-controls="example-queries-modal"
              aria-expanded={isExampleQueriesOpen}
              aria-haspopup="dialog"
              className="glass-panel flex items-center hover:scale-105 transition-all duration-300 px-4 py-2 rounded-lg border border-white/20 hover:border-white/30"
              size="sm"
              variant="light"
              onClick={() => setIsExampleQueriesOpen(true)}
            >
              <svg
                className="mr-1.5"
                fill="none"
                height="16"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M9 11l3 3L22 4" />
                <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11" />
              </svg>
              Example Queries
            </Button>

            {/* Dark Mode Toggle removed - using navbar theme switch instead */}

            {/* Tab Navigation */}
            <Tabs
              classNames={{
                tabList: "ml-4 glass-panel rounded-lg p-1",
                tab: "px-4 py-2 data-[hover=true]:bg-white/5 transition-all",
                cursor: "bg-gradient-to-r from-blue-500/30 to-purple-500/30 shadow-lg",
                tabContent: "text-sm font-medium group-data-[selected=true]:text-white",
              }}
              selectedKey={activeTab}
              size="sm"
              variant="light"
              onSelectionChange={(key) => setActiveTab(key as TabKey)}
            >
              {tabs.map((tab) => (
                <Tab
                  key={tab.key}
                  className={activeTab === tab.key ? "active" : ""}
                  title={tab.label}
                />
              ))}
            </Tabs>
          </div>
        </div>
      </header>

      {/* Example Queries Modal */}
      <ExampleQueriesDisplay
        isOpen={isExampleQueriesOpen}
        queries={exampleQueriesData} // Use the defined data
        onClose={() => setIsExampleQueriesOpen(false)}
      />
    </>
  );
};

export default NavigationHeader;
