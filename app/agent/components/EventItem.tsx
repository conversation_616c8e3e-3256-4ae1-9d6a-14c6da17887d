"use client";

import React, { useState, useMemo, useCallback } from "react";
import ReactMarkdown from "react-markdown";

import { useAgentStore } from "../store/agentStore";
import { AGENT_DISPLAY_NAMES, AgentName, EventHistoryItem, EventType } from "../types/index";
import { getAgentColorScheme, getEventTypeColorScheme } from "../utils/colorUtils";

import AgentOutputReportButton from "./AgentOutputReportButton";
import JsonContentModal from "./JsonContentModal";
import TimeStamp from "./TimeStamp";

interface EventItemProps {
  event: EventHistoryItem;
}

const EventItem: React.FC<EventItemProps> = ({ event }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isJsonModalOpen, setIsJsonModalOpen] = useState(false);
  const [selectedJsonContent, setSelectedJsonContent] = useState("");
  const [selectedJsonTitle, setSelectedJsonTitle] = useState("");
  const { agentState } = useAgentStore();
  // isDarkMode is no longer needed here as CSS variables will handle theme switching.

  // Memoize expensive computations
  const eventMetadata = useMemo(() => ({
    hasToolCall: event.type === EventType.TOOL_CALL,
    hasToolResponse: event.type === EventType.TOOL_RESPONSE || !!event.tool_response,
    isError: event.is_error || false,
  }), [event.type, event.tool_response, event.is_error]);

  const { hasToolCall, hasToolResponse, isError } = eventMetadata;

  // Get agent badge style using the centralized color system
  const getAgentBadgeStyle = (agentAuthor?: string) => {
    if (!agentAuthor) {
      // Fallback for events without a specific agent author, or if author is not in AgentName enum
      return "bg-neutral-200 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-200 px-2 py-0.5 rounded-full text-xs font-medium";
    }

    // Attempt to cast string to AgentName. This assumes event.author strings match AgentName enum keys.
    const agentName = agentAuthor as AgentName;
    const agentTextColorClass = getAgentColorScheme(agentName);

    // Define a neutral badge background that works in light/dark mode
    const badgeBackgroundClass = "bg-neutral-200 dark:bg-neutral-700";

    return `${badgeBackgroundClass} ${agentTextColorClass} px-2 py-0.5 rounded-full text-xs font-medium`;
  };

  // Get appropriate styling based on event type using the centralized color system
  const getEventTypeStyles = () => {
    let eventTypeKey = "default";

    if (isError) {
      eventTypeKey = "error";
    } else if (event.type === EventType.TOOL_CALL) {
      eventTypeKey = "tool-call";
    } else if (event.type === EventType.TOOL_RESPONSE) {
      eventTypeKey = "tool-response";
    } else if (event.type === EventType.AGENT_RESPONSE) {
      eventTypeKey = "agent-response";
    } else if (event.type === EventType.USER_INPUT) {
      eventTypeKey = "user-input";
    }

    const colorScheme = getEventTypeColorScheme(eventTypeKey); // isDarkMode removed

    // colorScheme.accent and colorScheme.bg should now be semantic Tailwind classes
    // Ensure accent is defined in EventColorScheme or handle if optional
    return `border-l-4 ${colorScheme.accent || "border-transparent"} ${colorScheme.bg}`;
  };

  // Get event icon based on type
  const getEventIcon = () => {
    if (
      event.type === EventType.TOOL_CALL ||
      event.content?.parts?.[0]?.function_call ||
      (event.content?.parts?.[0] && "functionCall" in event.content.parts[0])
    ) {
      return (
        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-status-warning-bg text-fg-primary flex items-center justify-center">
          <svg
            fill="none"
            height="14"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            width="14"
            xmlns="http://www.w3.org/2000/svg"
          >
            <polyline points="16 18 22 12 16 6" />
            <polyline points="8 6 2 12 8 18" />
          </svg>
        </div>
      );
    }

    if (
      event.type === EventType.TOOL_RESPONSE ||
      event.content?.parts?.[0]?.function_response ||
      (event.content?.parts?.[0] && "functionResponse" in event.content.parts[0])
    ) {
      return (
        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-status-info-bg text-fg-primary flex items-center justify-center">
          <svg
            fill="none"
            height="14"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            width="14"
            xmlns="http://www.w3.org/2000/svg"
          >
            <polyline points="20 6 9 17 4 12" />
          </svg>
        </div>
      );
    }

    if (event.is_error) {
      return (
        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-status-error-bg text-fg-primary flex items-center justify-center">
          <svg
            fill="none"
            height="14"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            width="14"
            xmlns="http://www.w3.org/2000/svg"
          >
            <line x1="18" x2="6" y1="6" y2="18" />
            <line x1="6" x2="18" y1="6" y2="18" />
          </svg>
        </div>
      );
    }

    if (event.content?.role === "user") {
      return (
        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-status-success-bg text-fg-primary flex items-center justify-center">
          <svg
            fill="none"
            height="14"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            width="14"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
            <circle cx="12" cy="7" r="4" />
          </svg>
        </div>
      );
    }

    return (
      <div className="flex-shrink-0 w-6 h-6 rounded-full bg-bg-tertiary text-fg-primary flex items-center justify-center">
        <svg
          fill="none"
          height="14"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          viewBox="0 0 24 24"
          width="14"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="12" cy="12" r="10" />
          <line x1="12" x2="12" y1="8" y2="12" />
          <line x1="12" x2="12.01" y1="16" y2="16" />
        </svg>
      </div>
    );
  };

  // Memoized JSON parsing functions
  const jsonHelpers = useMemo(() => {
    const hasJsonInMarkdown = (text: string): boolean => {
      const jsonCodeBlockRegex = /```json\s*([\s\S]*?)```/g;
      return jsonCodeBlockRegex.test(text);
    };

    const extractJsonFromMarkdown = (text: string): string | null => {
      const jsonCodeBlockRegex = /```json\s*([\s\S]*?)```/g;
      const match = jsonCodeBlockRegex.exec(text);
      return match ? match[1].trim() : null;
    };

    return { hasJsonInMarkdown, extractJsonFromMarkdown };
  }, []);

  const { hasJsonInMarkdown, extractJsonFromMarkdown } = jsonHelpers;

  // Optimized callback functions
  const handleOpenJsonModal = useCallback((jsonContent: string, title: string) => {
    setSelectedJsonContent(jsonContent);
    setSelectedJsonTitle(title);
    setIsJsonModalOpen(true);
  }, []);

  const handleToggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  const handleCloseModal = useCallback(() => {
    setIsJsonModalOpen(false);
  }, []);

  // Format JSON with a button to open modal
  const renderFormattedJson = (jsonContent: string, title: string = "JSON Content") => {
    try {
      const parsed = JSON.parse(jsonContent);
      const formattedJson = JSON.stringify(parsed, null, 2);
      const preview = formattedJson.length > 200 ? `${formattedJson.substring(0, 200)}...` : formattedJson;

      return (
        <div className="text-xs bg-content2 p-3 rounded w-full border border-divider">
          <div className="flex items-center justify-between mb-2">
            <span className="text-foreground font-medium">JSON Data</span>
            <button
              className="px-3 py-1 bg-primary text-primary-foreground hover:bg-primary/90 rounded-md text-xs font-medium transition-colors flex items-center gap-1"
              onClick={() => handleOpenJsonModal(jsonContent, title)}
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                />
              </svg>
              View Report
            </button>
          </div>
          <pre className="text-foreground-600 font-mono text-xs whitespace-pre-wrap break-words overflow-hidden">
            {preview}
          </pre>
        </div>
      );
    } catch (e) {
      return (
        <div className="text-xs bg-content2 p-3 rounded border border-divider">
          <div className="flex items-center justify-between mb-2">
            <span className="text-foreground font-medium">JSON Data</span>
            <button
              className="px-3 py-1 bg-primary text-primary-foreground hover:bg-primary/90 rounded-md text-xs font-medium transition-colors flex items-center gap-1"
              onClick={() => handleOpenJsonModal(jsonContent, title)}
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                />
              </svg>
              View Content
            </button>
          </div>
          <div className="text-foreground-600 text-xs">
            Invalid JSON format
          </div>
        </div>
      );
    }
  };

  const getEventSummary = () => {
    // Display type-specific labels first
    if (event.type === EventType.AGENT_RESPONSE) {
      return "Agent Response";
    }

    // Use explicit tool_name field if available
    if (event.tool_name) {
      if (event.type === EventType.TOOL_CALL) {
        return `Tool Call: ${event.tool_name}`;
      }
      if (event.type === EventType.TOOL_RESPONSE) {
        return `Tool Response: ${event.tool_name}`;
      }
    }

    // Fallback to content parsing
    if (event.content?.parts?.[0]?.function_call) {
      return `Tool Call: ${event.content.parts[0].function_call.name}`;
    }

    if (event.content?.parts?.[0] && "functionCall" in event.content.parts[0]) {
      return `Tool Call: ${(event.content.parts[0] as any).functionCall.name}`;
    }

    if (event.content?.parts?.[0]?.function_response) {
      return `Tool Response: ${event.content.parts[0].function_response.name}`;
    }

    if (event.content?.parts?.[0] && "functionResponse" in event.content.parts[0]) {
      return `Tool Response: ${(event.content.parts[0] as any).functionResponse.name}`;
    }

    // Only extract a preview of text content if not JSON
    if (event.content?.parts?.[0]?.text) {
      const text = event.content.parts[0].text;

      // Check if content is JSON (starts with { or [)
      if (text.trim().startsWith("{") || text.trim().startsWith("[")) {
        return "JSON Content";
      }

      // Check if content contains JSON in markdown code blocks
      if (hasJsonInMarkdown(text)) {
        return "Markdown with JSON Content";
      }

      // Otherwise return first line of text or truncate
      const firstLine = text.split("\n")[0];

      return firstLine.length > 100 ? `${firstLine.substring(0, 100)}...` : firstLine;
    }

    return "Event";
  };

  // Get event subtitle (tool arguments or other details)
  const getEventSubtitle = () => {
    // For tool responses, show a compact summary
    if (hasToolResponse && event.tool_response) {
      return `Response from ${event.tool_response.name}`;
    }

    // Handle tool call args
    if (event.tool_call && event.tool_call.args) {
      const { args } = event.tool_call;

      if (typeof args === "object" && args !== null) {
        // Get the first few key-value pairs for compact display
        const entries = Object.entries(args).slice(0, 2);

        if (entries.length > 0) {
          return entries.map(([key, value]) => `${key}: ${JSON.stringify(value)}`).join(", ");
        }
      }
    }

    // Fallback to content parsing
    if (event.content?.parts?.[0]?.function_call) {
      const { args } = event.content.parts[0].function_call;

      if (typeof args === "object" && args !== null) {
        // Get the first few key-value pairs for compact display
        const entries = Object.entries(args).slice(0, 2);

        if (entries.length > 0) {
          return entries.map(([key, value]) => `${key}: ${JSON.stringify(value)}`).join(", ");
        }
      }
    }

    if (event.content?.parts?.[0] && "functionCall" in event.content.parts[0]) {
      const args = (event.content.parts[0] as any).functionCall.args;

      if (typeof args === "object" && args !== null) {
        // Get the first few key-value pairs for compact display
        const entries = Object.entries(args).slice(0, 2);

        if (entries.length > 0) {
          return entries.map(([key, value]) => `${key}: ${JSON.stringify(value)}`).join(", ");
        }
      }
    }

    return "";
  };

  return (
    <div className={`rounded-md ${getEventTypeStyles()} ${isExpanded ? "mb-1" : "mb-0.5"}`}>
      <div className="flex items-start gap-3 p-2">
        {getEventIcon()}

        <div className="flex-grow">
          <div className="flex justify-between items-center mb-1">
            <div className="flex items-center gap-2">
              {event.author && (
                <span className={getAgentBadgeStyle(event.author)}>
                  {AGENT_DISPLAY_NAMES[event.author as AgentName] || event.author}
                </span>
              )}
              {hasToolCall && (
                <span
                  key="tool-call-badge"
                  className="text-xs px-1.5 py-0.5 bg-status-info-bg text-fg-primary rounded-full"
                >
                  Tool Call
                </span>
              )}
              {hasToolResponse && (
                <span
                  key="tool-response-badge"
                  className="text-xs px-1.5 py-0.5 bg-status-success-bg text-fg-primary rounded-full"
                >
                  Tool Response
                </span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-fg-secondary">
                <TimeStamp
                  className="text-fg-secondary"
                  format="HH:mm:ss"
                  timestamp={event.timestamp}
                />
              </span>
            </div>
          </div>

          <p className="text-sm font-medium mt-0.5 text-fg-primary">{getEventSummary()}</p>

          {/* Only show summary (not full content) when not expanded */}
          {!isExpanded && getEventSubtitle() && (
            <p className="text-xs text-fg-secondary font-mono mt-0.5">{getEventSubtitle()}</p>
          )}

          {/* Indicators */}
          <div className="flex mt-1.5 gap-1">
            {/* Always ensure tool responses have a Show Details button */}
            {(hasToolCall ||
              hasToolResponse ||
              event.type === EventType.AGENT_RESPONSE ||
              (event.content?.parts?.[0]?.text &&
                (event.content.parts[0].text.trim().startsWith("{") ||
                  event.content.parts[0].text.trim().startsWith("[")))) && (
              <button
                key="show-details-button"
                className="text-xs px-1.5 py-0.5 bg-bg-tertiary text-fg-accent rounded-full flex items-center gap-1 hover:bg-bg-highlight transition-colors"
                onClick={handleToggleExpanded}
              >
                <span>{isExpanded ? "Hide Details" : "Show Details"}</span>
                <svg
                  className={`w-3 h-3 transition-transform ${isExpanded ? "rotate-180" : ""}`}
                  fill="none"
                  height="24"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <polyline points="6 9 12 15 18 9" />
                </svg>
              </button>
            )}
            {event.is_final_response && (
              <span
                key="final-indicator"
                className="text-xs px-1.5 py-0.5 bg-status-success-bg text-fg-primary rounded-full"
              >
                Final
              </span>
            )}
            {event.actions?.state_delta && (
              <span
                key="state-indicator"
                className="text-xs px-1.5 py-0.5 bg-status-info-bg text-fg-primary rounded-full"
              >
                State
              </span>
            )}
            {event.is_error && (
              <span
                key="error-indicator"
                className="text-xs px-1.5 py-0.5 bg-status-error-bg text-fg-primary rounded-full"
              >
                Error
              </span>
            )}

            {/* Agent Output Report Button */}
            {event.type === EventType.AGENT_RESPONSE && event.author && (
              <AgentOutputReportButton
                key="agent-report-button"
                agentName={event.author}
                agentState={agentState}
              />
            )}
          </div>

          {/* Expandable Details - Tool Calls and JSON Content */}
          {isExpanded && (
            <div className="mt-3 border-t pt-3">
              {/* Header based on content type */}
              <h4 className="text-sm font-medium mb-2">
                {hasToolCall || hasToolResponse
                  ? "Tool Call Details"
                  : event.type === EventType.AGENT_RESPONSE
                    ? "Agent Response Details"
                    : "Event Details"}
              </h4>

              {/* Tool Call Information */}
              {hasToolCall && event.tool_call && (
                <div className="mb-3 bg-content2 p-3 rounded-md border border-divider">
                  <div className="flex items-center mb-2">
                    <span className="text-xs px-1.5 py-0.5 bg-status-info-bg text-fg-primary rounded-full mr-2">
                      Tool
                    </span>
                    <span className="font-mono font-medium">
                      {event.tool_name || event.tool_call.name}
                    </span>
                  </div>
                </div>
              )}

              {/* Tool Response Information (if available) */}
              {hasToolResponse && event.tool_response && (
                <div className="mb-2 bg-content2 p-3 rounded-md border border-divider">
                  <div className="flex items-center mb-1">
                    <span className="text-xs px-1.5 py-0.5 bg-status-info-bg text-fg-primary rounded-full mr-2">
                      Tool Response
                    </span>
                    <span className="font-mono font-medium text-sm text-foreground-700 dark:text-foreground-300">
                      {event.tool_response.name}
                    </span>
                    {event.tool_response.status && (
                      <span
                        className={`ml-2 text-xs px-1.5 py-0.5 rounded-full ${event.tool_response.status === "success" ? "bg-status-success-bg" : "bg-status-error-bg"} text-fg-primary`}
                      >
                        {event.tool_response.status}
                      </span>
                    )}
                  </div>

                  {/* Render event.tool_response.response.result as Markdown if it's a string */}
                  {typeof event.tool_response.response?.result === "string" &&
                  event.tool_response.response.result.trim() !== "" ? (
                    <div className="markdown-content mt-2 p-2 border rounded border-divider bg-content2">
                      <div className="prose dark:prose-invert max-w-none text-sm">
                        <ReactMarkdown>{event.tool_response.response.result}</ReactMarkdown>
                      </div>
                    </div>
                  ) : event.tool_response.summary ? (
                    // Fallback to summary if no result string or result is not a string
                    <div className="text-sm mt-1 text-foreground-600 dark:text-foreground-400">
                      {event.tool_response.summary}
                    </div>
                  ) : null}

                  {/* Toggle to show raw event.tool_response.response object */}
                  {event.tool_response.response && (
                    <details className="mt-2 text-xs">
                      <summary className="cursor-pointer text-foreground-500 hover:text-foreground-700 dark:text-foreground-400 dark:hover:text-foreground-200">
                        View Raw Response Object
                      </summary>
                      <div className="mt-1">
                        {renderFormattedJson(JSON.stringify(event.tool_response.response, null, 2), "Tool Response Data")}
                      </div>
                    </details>
                  )}
                </div>
              )}

              {/* JSON Content Display */}
              {event.content?.parts?.[0]?.text && (
                <>
                  {/* Direct JSON content */}
                  {(event.content.parts[0].text.trim().startsWith("{") ||
                    event.content.parts[0].text.trim().startsWith("[")) && (
                    <div className="mb-2 bg-content2 p-3 rounded-md border border-divider">
                      <div className="flex items-center mb-2">
                        <span className="text-xs px-1.5 py-0.5 bg-status-info-bg text-fg-primary rounded-full mr-2">
                          JSON Content
                        </span>
                      </div>

                      {/* Prettified JSON */}
                      {renderFormattedJson(event.content.parts[0].text, "Agent Response JSON")}
                    </div>
                  )}

                  {/* JSON in Markdown code blocks */}
                  {hasJsonInMarkdown(event.content.parts[0].text) && (
                    <div className="mb-2 bg-content2 p-3 rounded-md border border-divider">
                      <div className="flex items-center mb-2">
                        <span className="text-xs px-1.5 py-0.5 bg-status-info-bg text-fg-primary rounded-full mr-2">
                          JSON in Markdown
                        </span>
                      </div>

                      {/* Extracted and Prettified JSON */}
                      {(() => {
                        const extractedJson = extractJsonFromMarkdown(event.content.parts[0].text);

                        if (extractedJson) {
                          return renderFormattedJson(extractedJson, "Extracted JSON from Markdown");
                        }

                        return (
                          <div className="text-xs bg-content2 p-2 rounded text-fg-secondary">
                            No valid JSON found in markdown
                          </div>
                        );
                      })()}
                    </div>
                  )}
                </>
              )}

              {/* Regular Text Content (for Agent Responses that aren't JSON and aren't already handled by markdown extraction) */}
              {event.type === EventType.AGENT_RESPONSE &&
                event.content?.parts?.[0]?.text &&
                !event.content.parts[0].text.trim().startsWith("{") &&
                !event.content.parts[0].text.trim().startsWith("[") &&
                !hasJsonInMarkdown(event.content.parts[0].text) && (
                  <div className="mb-2 bg-content2 p-3 rounded-md border border-divider">
                    <div className="whitespace-pre-wrap">{event.content.parts[0].text}</div>
                  </div>
                )}

              {/* If no displayable content is available */}
              {!hasToolCall && !hasToolResponse && !event.content?.parts?.[0]?.text && (
                <div className="text-sm text-fg-secondary">No details available.</div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* JSON Content Modal */}
      <JsonContentModal
        isOpen={isJsonModalOpen}
        jsonContent={selectedJsonContent}
        title={selectedJsonTitle}
        onClose={handleCloseModal}
      />
    </div>
  );
};

export default EventItem;
