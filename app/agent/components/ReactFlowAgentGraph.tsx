"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>s,
  Edge,
  <PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>,
  MiniMap,
  Node,
  Panel,
  Position,
  ReactFlow,
  useEdgesState,
  useNodesState,
} from "@xyflow/react";
import "@xyflow/react/dist/base.css";
import { useTheme } from "next-themes";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";

import { AgentName } from "../types";
import "./ReactFlowAgentGraph.css";

interface AgentNode {
  id: string;
  name: string;
  status: "idle" | "active" | "completed" | "error";
  type: "user" | "agent" | "tool";
}

interface AgentEdge {
  source: string;
  target: string;
  label?: string;
}

interface ReactFlowAgentGraphProps {
  nodes: AgentNode[];
  edges: AgentEdge[];
}

// Status colors for agent nodes - Turbo Flow inspired gradient colors
const statusColors = {
  idle: "#6b7280",
  active: "#2a8af6",
  completed: "#10b981",
  error: "#ef4444",
};

// Gradient colors for Turbo Flow theme
const gradientColors = {
  start: "#ae53ba",
  end: "#2a8af6",
};

// Type icons for different node types
const typeIcons = {
  user: "👤",
  agent: "🤖",
  tool: "🔧",
};

// Tooltip component for agent hover information
const AgentTooltip: React.FC<{
  agentData: AgentNode & { completionInfo?: string; lastActivity?: string };
  position: { x: number; y: number };
  isDarkMode: boolean;
}> = ({ agentData, position, isDarkMode }) => {
  const getStatusDescription = (status: string) => {
    switch (status) {
      case "idle":
        return "Waiting to start processing";
      case "active":
        return "Currently processing tasks";
      case "completed":
        return "All tasks completed successfully";
      case "error":
        return "Encountered an error during processing";
      default:
        return "Status unknown";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "idle":
        return isDarkMode ? "#6b7280" : "#9ca3af";
      case "active":
        return "#2a8af6";
      case "completed":
        return "#10b981";
      case "error":
        return "#ef4444";
      default:
        return "#6b7280";
    }
  };

  return (
    <div
      className="agent-tooltip"
      style={{
        position: "absolute",
        left: position.x + 15,
        top: position.y - 60,
        zIndex: 1000,
        pointerEvents: "none",
        background: isDarkMode ? "rgba(17, 17, 17, 0.95)" : "rgba(255, 255, 255, 0.95)",
        border: isDarkMode ? "1px solid rgba(255, 255, 255, 0.1)" : "1px solid rgba(0, 0, 0, 0.1)",
        borderRadius: "8px",
        padding: "12px",
        boxShadow: isDarkMode ? "0 8px 32px rgba(0, 0, 0, 0.3)" : "0 8px 32px rgba(0, 0, 0, 0.1)",
        backdropFilter: "blur(10px)",
        maxWidth: "280px",
        fontSize: "12px",
        animation: "fadeIn 0.2s ease-out",
      }}
    >
      <div className="flex items-center mb-2">
        <div className="text-base mr-2">{typeIcons[agentData.type]}</div>
        <div>
          <div
            className="font-semibold text-sm"
            style={{ color: isDarkMode ? "#ffffff" : "#1a1a1a" }}
          >
            {agentData.name}
          </div>
          <div
            className="flex items-center text-xs mt-1"
            style={{ color: getStatusColor(agentData.status) }}
          >
            <div
              className="w-2 h-2 rounded-full mr-1.5"
              style={{ backgroundColor: getStatusColor(agentData.status) }}
            />
            {agentData.status.toUpperCase()}
          </div>
        </div>
      </div>

      <div
        className="text-xs mb-2"
        style={{ color: isDarkMode ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.7)" }}
      >
        {getStatusDescription(agentData.status)}
      </div>

      {agentData.completionInfo && (
        <div
          className="text-xs mb-1"
          style={{ color: isDarkMode ? "rgba(255, 255, 255, 0.6)" : "rgba(0, 0, 0, 0.6)" }}
        >
          <strong>Progress:</strong> {agentData.completionInfo}
        </div>
      )}

      {agentData.lastActivity && (
        <div
          className="text-xs"
          style={{ color: isDarkMode ? "rgba(255, 255, 255, 0.6)" : "rgba(0, 0, 0, 0.6)" }}
        >
          <strong>Last Activity:</strong> {agentData.lastActivity}
        </div>
      )}
    </div>
  );
};

// Custom node component for agents - optimized with better memoization
const AgentNodeComponent = React.memo(({ data }: { data: any }) => {
  const agentData = data as AgentNode;
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  const [showTooltip, setShowTooltip] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Use useCallback to prevent unnecessary re-renders
  const handleMouseEnter = useCallback((e: React.MouseEvent) => {
    setMousePosition({ x: 0, y: 0 });
    setShowTooltip(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setShowTooltip(false);
  }, []);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    setMousePosition({ x: 0, y: 0 });
  }, []);

  // Memoize enhanced agent data to prevent recalculation
  const enhancedAgentData = useMemo(() => ({
    ...agentData,
    completionInfo:
      agentData.status === "completed"
        ? "Task completed"
        : agentData.status === "active"
          ? "Processing..."
          : agentData.status === "error"
            ? "Failed"
            : "Pending",
    lastActivity:
      agentData.status === "active"
        ? "Now"
        : agentData.status === "completed"
          ? "Recently"
          : "Not started",
  }), [agentData]);

  // Memoize styles to prevent recalculation
  const nodeStyle = useMemo(() => ({
    background:
      agentData.status === "active"
        ? `linear-gradient(135deg, ${gradientColors.start}20, ${gradientColors.end}20)`
        : isDarkMode
          ? "rgba(17, 17, 17, 0.9)"
          : "rgba(255, 255, 255, 0.95)",
    border: `1px solid ${
      agentData.status === "active"
        ? statusColors.active
        : isDarkMode
          ? "rgba(255, 255, 255, 0.1)"
          : "rgba(0, 0, 0, 0.1)"
    }`,
    minWidth: "180px",
    boxShadow:
      agentData.status === "active"
        ? `0 0 30px ${statusColors.active}40, inset 0 0 20px ${statusColors.active}10`
        : isDarkMode
          ? "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
          : "0 4px 6px -1px rgba(0, 0, 0, 0.05)",
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
  }), [agentData.status, isDarkMode]);

  const handleStyle = useMemo(() => ({
    background: agentData.status === "active" ? statusColors.active : isDarkMode ? "#555" : "#999",
    width: 8,
    height: 8,
    border: isDarkMode ? "2px solid #1a1a1a" : "2px solid #fff",
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
  }), [agentData.status, isDarkMode]);

  return (
    <>
      <Handle
        position={Position.Top}
        style={handleStyle}
        type="target"
      />
      <div
        className="px-4 py-3 rounded-lg relative agent-node-content"
        style={nodeStyle}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onMouseMove={handleMouseMove}
      >
        <div className="flex items-center">
          <div className="text-lg mr-2">{typeIcons[agentData.type]}</div>
          <div>
            <div className={`text-sm font-semibold transition-colors duration-300 ${isDarkMode ? "text-white" : "text-gray-900"}`}>
              {agentData.name}
            </div>
            <div
              className="text-xs font-medium transition-colors duration-300 mt-0.5"
              style={{
                color:
                  agentData.status === "active"
                    ? statusColors.active
                    : isDarkMode
                      ? "rgba(255, 255, 255, 0.6)"
                      : "rgba(0, 0, 0, 0.6)",
              }}
            >
              {agentData.status.toUpperCase()}
            </div>
          </div>
        </div>
        {/* Add a pulsing indicator for active agents */}
        {agentData.status === "active" && (
          <div className="absolute -top-1 -right-1 h-3 w-3">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75" />
            <span className="relative inline-flex rounded-full h-3 w-3 bg-blue-500" />
          </div>
        )}
      </div>
      <Handle
        position={Position.Bottom}
        style={handleStyle}
        type="source"
      />

      {/* Render tooltip when hovering */}
      {showTooltip && (
        <AgentTooltip
          agentData={enhancedAgentData}
          isDarkMode={isDarkMode}
          position={mousePosition}
        />
      )}
    </>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for better memoization
  const prev = prevProps.data as AgentNode;
  const next = nextProps.data as AgentNode;
  
  return (
    prev.id === next.id &&
    prev.name === next.name &&
    prev.status === next.status &&
    prev.type === next.type
  );
});

// Add display name for debugging
AgentNodeComponent.displayName = "AgentNodeComponent";

// Optimized ReactFlow component with better live update handling
const ReactFlowAgentGraph = React.memo(
  ({ nodes: agentNodes, edges: agentEdges }: ReactFlowAgentGraphProps) => {
    const { theme } = useTheme();
    const isDarkMode = theme === "dark";
    
    // Track previous state for comparison
    const prevAgentNodesRef = useRef<AgentNode[]>([]);
    const prevActiveNodeIds = useRef<string>("");

    // Memoize nodeTypes to ensure React Flow doesn't detect changes
    const nodeTypes = useMemo(
      () => ({
        custom: AgentNodeComponent,
      }),
      [],
    );

    // Define position map for specific agents - centered layout without Data Collector
    const positionMap = useMemo(() => ({
      // Top level - Parallel orchestrator
      [AgentName.PARALLEL_AGENT]: { x: 600, y: 0 },

      // Middle level - Parallel specialized agents (wider spacing)
      [AgentName.FINANCIAL_ANALYST]: { x: 0, y: 250 },
      [AgentName.RISK_ASSESSMENT]: { x: 300, y: 250 },
      [AgentName.COMPLIANCE]: { x: 600, y: 250 },
      [AgentName.LEGAL_REVIEW]: { x: 900, y: 250 },
      [AgentName.ENTITY_MAPPING]: { x: 1200, y: 250 },

      // Bottom level - Report generation
      [AgentName.REPORT_INSIGHTS]: { x: 600, y: 500 },
    } as Partial<Record<AgentName, { x: number; y: number; }>>), []);

    // Convert agent nodes to React Flow nodes - memoize based on actual content changes
    const initialNodes = useMemo(() => {
      return agentNodes.map((node) => ({
        id: node.id,
        type: "custom",
        position: positionMap[node.id as AgentName] || { x: 0, y: 0 },
        data: { ...node },
        className: `status-${node.status}`,
      })) as Node[];
    }, [agentNodes, positionMap]); // Depend on actual content for proper updates

    // Convert agent edges to React Flow edges with memoization
    const initialEdges = useMemo(() => {
      return agentEdges.map((edge, index) => ({
        id: `edge-${index}`,
        source: edge.source,
        target: edge.target,
        label: edge.label,
        type: "smoothstep",
        animated: false,
        style: {
          stroke: isDarkMode ? "#6b7280" : "#9ca3af",
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        },
        markerEnd: {
          type: MarkerType.ArrowClosed,
          color: isDarkMode ? "#6b7280" : "#9ca3af",
        },
      })) as Edge[];
    }, [agentEdges, isDarkMode]);

    const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
    const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

    // Optimized node update function with debouncing to reduce flickering
    const updateNodesOptimized = useCallback((newAgentNodes: AgentNode[]) => {
      const nodeDataMap = new Map(newAgentNodes.map((node) => [node.id, node]));

      setNodes((currentNodes) => {
        let hasChanges = false;
        const updatedNodes = currentNodes.map((currentNode) => {
          const newData = nodeDataMap.get(currentNode.id);

          if (newData && (
            currentNode.data.status !== newData.status ||
            currentNode.data.name !== newData.name ||
            currentNode.data.type !== newData.type
          )) {
            hasChanges = true;
            return {
              ...currentNode,
              data: { ...newData },
              className: `status-${newData.status}`,
            };
          }

          return currentNode;
        });

        // Only return new array if something actually changed
        return hasChanges ? updatedNodes : currentNodes;
      });
    }, [setNodes]);

    // Update nodes when agentNodes prop changes with optimized approach
    useEffect(() => {
      const currentNodeIds = agentNodes.map(n => `${n.id}:${n.status}`).join(',');
      const prevNodeIds = prevAgentNodesRef.current.map(n => `${n.id}:${n.status}`).join(',');
      
      if (currentNodeIds !== prevNodeIds) {
        updateNodesOptimized(agentNodes);
        prevAgentNodesRef.current = [...agentNodes];
      }
    }, [agentNodes, updateNodesOptimized]);

    // Create a memoized string representation of active nodes for dependency tracking
    const activeNodeIds = useMemo(() => {
      return agentNodes
        .filter((node) => node.status === "active")
        .map((node) => node.id)
        .join(",");
    }, [agentNodes]);

    // Optimized edge update function
    const updateEdgesOptimized = useCallback((activeNodes: string[]) => {
      setEdges((currentEdges) => {
        const updatedEdges = currentEdges.map((currentEdge) => {
          const edgeIndex = parseInt(currentEdge.id.split("-")[1]);
          const originalEdge = agentEdges[edgeIndex];

          if (originalEdge) {
            const isAnimated =
              activeNodes.includes(originalEdge.source) ||
              activeNodes.includes(originalEdge.target);

            const needsUpdate = currentEdge.animated !== isAnimated;

            if (needsUpdate) {
              return {
                ...currentEdge,
                animated: isAnimated,
                style: {
                  ...currentEdge.style,
                  stroke: isAnimated
                    ? "url(#edge-gradient)"
                    : isDarkMode
                      ? "rgba(255, 255, 255, 0.2)"
                      : "rgba(0, 0, 0, 0.2)",
                  strokeWidth: isAnimated ? 2.5 : 1.5,
                  transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                },
                markerEnd: isAnimated
                  ? "edge-circle"
                  : {
                      type: MarkerType.ArrowClosed,
                      color: isDarkMode ? "rgba(255, 255, 255, 0.2)" : "rgba(0, 0, 0, 0.2)",
                    },
              };
            }
          }

          return currentEdge;
        });

        const hasChanges = updatedEdges.some((edge, index) => 
          edge.animated !== currentEdges[index].animated
        );

        return hasChanges ? updatedEdges : currentEdges;
      });
    }, [setEdges, agentEdges, isDarkMode]);

    // Update edges when active nodes change
    useEffect(() => {
      if (activeNodeIds !== prevActiveNodeIds.current) {
        const activeNodes = activeNodeIds ? activeNodeIds.split(",") : [];

        updateEdgesOptimized(activeNodes);
        prevActiveNodeIds.current = activeNodeIds;
      }
    }, [activeNodeIds, updateEdgesOptimized]);

    return (
      <div
        className={`w-full h-full turbo-flow-container ${isDarkMode ? "dark" : "light"}`}
        style={{
          minHeight: "400px",
        }}
      >
        <ReactFlow
          fitView
          edges={edges}
          fitViewOptions={{
            padding: 0.2,
          }}
          nodeTypes={nodeTypes}
          nodes={nodes}
          proOptions={{ hideAttribution: true }}
          style={{ background: "transparent" }}
          onEdgesChange={onEdgesChange}
          onNodesChange={onNodesChange}
        >
          <Background
            color={isDarkMode ? "rgba(255, 255, 255, 0.02)" : "rgba(0, 0, 0, 0.02)"}
            gap={20}
            size={1}
            variant={BackgroundVariant.Dots}
          />
          <Controls />
          <MiniMap
            maskColor={isDarkMode ? "rgba(13, 13, 13, 0.9)" : "rgba(245, 245, 245, 0.9)"}
            nodeColor={(node) => {
              if (node.data && typeof node.data === "object" && "status" in node.data) {
                const status = (node.data as any).status as string;

                return statusColors[status as keyof typeof statusColors] || statusColors.idle;
              }

              return statusColors.idle;
            }}
            style={{
              backgroundColor: isDarkMode ? "rgba(17, 17, 17, 0.9)" : "rgba(255, 255, 255, 0.9)",
              border: isDarkMode
                ? "1px solid rgba(255, 255, 255, 0.1)"
                : "1px solid rgba(0, 0, 0, 0.1)",
            }}
          />
          
          {/* SVG definitions for gradient edges */}
          <svg style={{ position: "absolute", width: 0, height: 0 }}>
            <defs>
              <linearGradient id="edge-gradient">
                <stop offset="0%" stopColor={gradientColors.start} />
                <stop offset="100%" stopColor={gradientColors.end} />
              </linearGradient>
              <marker
                id="edge-circle"
                markerHeight="10"
                markerUnits="strokeWidth"
                markerWidth="10"
                orient="auto"
                refX="0"
                refY="0"
                viewBox="-5 -5 10 10"
              >
                <circle
                  cx="0"
                  cy="0"
                  fill={gradientColors.end}
                  r="2"
                  stroke={gradientColors.end}
                  strokeOpacity="0.75"
                />
              </marker>
            </defs>
          </svg>
          
          {/* Legend Panel */}
          <Panel className="legend-panel" position="bottom-left">
            <div
              className="p-3 rounded-lg"
              style={{
                background: isDarkMode ? "rgba(17, 17, 17, 0.95)" : "rgba(255, 255, 255, 0.95)",
                border: isDarkMode
                  ? "1px solid rgba(255, 255, 255, 0.1)"
                  : "1px solid rgba(0, 0, 0, 0.1)",
                backdropFilter: "blur(10px)",
              }}
            >
              <h4
                className={`text-xs font-semibold mb-2 ${isDarkMode ? "text-gray-300" : "text-gray-700"}`}
              >
                Agent Status
              </h4>
              <div className="space-y-1.5">
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: statusColors.idle }}
                  />
                  <span className={`text-xs ${isDarkMode ? "text-gray-400" : "text-gray-600"}`}>
                    Idle - Waiting to start
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full animate-pulse"
                    style={{ backgroundColor: statusColors.active }}
                  />
                  <span className={`text-xs ${isDarkMode ? "text-gray-400" : "text-gray-600"}`}>
                    Active - Currently processing
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: statusColors.completed }}
                  />
                  <span className={`text-xs ${isDarkMode ? "text-gray-400" : "text-gray-600"}`}>
                    Completed - Task finished
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: statusColors.error }}
                  />
                  <span className={`text-xs ${isDarkMode ? "text-gray-400" : "text-gray-600"}`}>
                    Error - Task failed
                  </span>
                </div>
              </div>
            </div>
          </Panel>
        </ReactFlow>
      </div>
    );
  },
);

// Add display name for debugging
ReactFlowAgentGraph.displayName = "ReactFlowAgentGraph";

// Export the optimized component
export default ReactFlowAgentGraph;