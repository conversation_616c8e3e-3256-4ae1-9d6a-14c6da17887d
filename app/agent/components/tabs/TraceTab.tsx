"use client";

import dynamic from "next/dynamic";
import React, { useEffect, useState } from "react";

import { useAgentStore } from "../../store/agentStore";

// Dynamically import ReactJsonView to avoid SSR issues
const ReactJsonView = dynamic(() => import("@microlink/react-json-view"), {
  ssr: false,
  loading: () => <div className="text-center text-foreground-500">Loading JSON viewer...</div>,
});

interface TraceSpan {
  name: string;
  span_id: number;
  trace_id: number;
  start_time: number;
  end_time: number;
  attributes: Record<string, any>;
  parent_span_id?: number;
}

interface TraceData {
  trace?: TraceSpan[];
  [key: string]: any;
}

interface ProcessedSpan extends TraceSpan {
  duration: number;
  children: ProcessedSpan[];
  level: number;
  agentName?: string;
  isAgent: boolean;
}

const TraceTab: React.FC = () => {
  const [traceData, setTraceData] = useState<TraceData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<"timeline" | "tree" | "raw">("timeline");

  const { sessionId, activeTab } = useAgentStore();

  const fetchTraceData = async () => {
    if (!sessionId) {
      setError("No session ID available");

      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

      if (!backendUrl) {
        throw new Error("Backend URL not configured");
      }

      const response = await fetch(`${backendUrl}/debug/trace/session/${sessionId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch trace data: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Only log in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log("Fetched trace data:", data);
        console.log("Trace array:", data.trace);
      }
      setTraceData(data);
    } catch (err) {
      console.error("Error fetching trace data:", err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when tab becomes active or sessionId changes
  useEffect(() => {
    if (activeTab === "trace" && sessionId) {
      fetchTraceData();
    }
  }, [activeTab, sessionId]);

  const handleRefresh = () => {
    fetchTraceData();
  };

  // Process trace data for visualization
  const processTraceData = (spans: TraceSpan[]): ProcessedSpan[] => {
    const spanMap = new Map<number, ProcessedSpan>();
    const rootSpans: ProcessedSpan[] = [];

    // First pass: create processed spans
    spans.forEach((span) => {
      const processed: ProcessedSpan = {
        ...span,
        duration: span.end_time - span.start_time,
        children: [],
        level: 0,
        isAgent: span.name.includes("agent_run"),
        agentName: span.name.includes("agent_run") ? span.name.match(/\[(.+?)\]/)?.[1] : undefined,
      };

      spanMap.set(span.span_id, processed);
    });

    // Second pass: build hierarchy
    spans.forEach((span) => {
      const processed = spanMap.get(span.span_id)!;

      if (span.parent_span_id && spanMap.has(span.parent_span_id)) {
        const parent = spanMap.get(span.parent_span_id)!;

        parent.children.push(processed);
        processed.level = parent.level + 1;
      } else {
        rootSpans.push(processed);
      }
    });

    return rootSpans;
  };

  const formatDuration = (nanoseconds: number): string => {
    const milliseconds = nanoseconds / 1_000_000;

    if (milliseconds < 1000) {
      return `${milliseconds.toFixed(1)}ms`;
    }

    return `${(milliseconds / 1000).toFixed(2)}s`;
  };

  const formatTime = (nanoseconds: number): string => {
    const date = new Date(nanoseconds / 1_000_000);

    return date.toLocaleTimeString();
  };

  const renderTimelineView = (spans: ProcessedSpan[]) => {
    if (!spans.length) return null;

    // Sort by start time
    const sortedSpans = [...spans].sort((a, b) => a.start_time - b.start_time);
    const minTime = Math.min(...sortedSpans.map((s) => s.start_time));
    const maxTime = Math.max(...sortedSpans.map((s) => s.end_time));
    const totalDuration = maxTime - minTime;

    return (
      <div className="space-y-2">
        <h4 className="font-medium text-sm text-foreground-700 mb-3">Execution Timeline</h4>
        {sortedSpans.map((span, index) => {
          const startOffset = ((span.start_time - minTime) / totalDuration) * 100;
          const width = ((span.end_time - span.start_time) / totalDuration) * 100;

          return (
            <div key={span.span_id} className="relative">
              <div className="flex items-center text-xs mb-1">
                <span
                  className={`font-medium ${span.isAgent ? "text-blue-600 dark:text-blue-300" : "text-foreground-700"}`}
                >
                  {span.agentName || span.name}
                </span>
                <span className="ml-2 text-foreground-500">{formatDuration(span.duration)}</span>
                <span className="ml-2 text-foreground-400">{formatTime(span.start_time)}</span>
              </div>
              <div className="relative h-6 glass-panel rounded border border-white/10">
                <div
                  className={`absolute h-full rounded ${
                    span.isAgent
                      ? "bg-gradient-to-r from-blue-500/50 to-purple-500/50 hover:from-blue-500/70 hover:to-purple-500/70 dark:from-blue-500/50 dark:to-purple-500/50 light:from-blue-400/40 light:to-purple-400/40"
                      : "bg-gradient-to-r from-gray-500/30 to-gray-600/30 hover:from-gray-500/50 hover:to-gray-600/50 dark:from-gray-500/30 dark:to-gray-600/30 light:from-gray-400/20 light:to-gray-500/20"
                  } transition-all cursor-pointer`}
                  style={{
                    left: `${startOffset}%`,
                    width: `${Math.max(width, 0.5)}%`,
                  }}
                  title={`${span.name}\nDuration: ${formatDuration(span.duration)}\nStart: ${formatTime(span.start_time)}`}
                />
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderTreeView = (spans: ProcessedSpan[], level = 0) => {
    return spans.map((span) => (
      <div key={span.span_id} className="mb-2">
        <div
          className="flex items-center p-3 rounded-lg glass-panel hover:bg-white/5 transition-all border border-transparent hover:border-white/10"
          style={{ marginLeft: `${level * 20}px` }}
        >
          <div
            className={`w-3 h-3 rounded-full mr-3 ${span.isAgent ? "bg-blue-500 dark:bg-blue-500 light:bg-blue-400" : "bg-foreground-400"}`}
          />
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <span
                className={`font-medium ${span.isAgent ? "text-blue-600 dark:text-blue-300" : "text-foreground-700"}`}
              >
                {span.agentName || span.name}
              </span>
              <div className="text-xs text-foreground-500 space-x-2">
                <span>{formatDuration(span.duration)}</span>
                <span>{formatTime(span.start_time)}</span>
              </div>
            </div>
            {span.isAgent && (
              <div className="text-xs text-foreground-500 mt-1">Agent Execution</div>
            )}
          </div>
        </div>
        {span.children.length > 0 && renderTreeView(span.children, level + 1)}
      </div>
    ));
  };

  const renderContent = () => {
    // Only log in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log("renderContent called, traceData:", traceData);
      console.log("traceData?.trace:", traceData?.trace);
    }

    // Check if trace data exists in different possible formats
    let traceArray = traceData?.trace;

    if (!traceArray && Array.isArray(traceData)) {
      traceArray = traceData;
      if (process.env.NODE_ENV === 'development') {
        console.log("Using traceData directly as array");
      }
    }

    if (!traceArray || !Array.isArray(traceArray) || traceArray.length === 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log("No valid trace array found, returning error message");
      }

      return (
        <div className="text-center py-8 glass-panel rounded-xl border border-white/10 p-8">
          <div className="w-16 h-16 mb-4 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center mx-auto">
            <svg
              className="w-8 h-8 text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
              />
            </svg>
          </div>
          <p className="text-foreground-300">No trace spans found in the data.</p>
          <p className="text-sm mt-2 text-foreground-500">
            Data structure: {JSON.stringify(Object.keys(traceData || {}))}
          </p>
          {traceData && (
            <details className="mt-4 text-left">
              <summary className="cursor-pointer text-blue-600">Show raw data</summary>
              <pre className="mt-2 p-2 bg-content2 rounded text-xs overflow-auto">
                {JSON.stringify(traceData, null, 2)}
              </pre>
            </details>
          )}
        </div>
      );
    }

    if (process.env.NODE_ENV === 'development') {
      console.log("Processing trace data with", traceArray.length, "spans");
    }
    const processedSpans = processTraceData(traceArray);

    if (process.env.NODE_ENV === 'development') {
      console.log("Processed spans:", processedSpans);
    }

    const allSpans = traceArray.map((span) => ({
      ...span,
      duration: span.end_time - span.start_time,
      children: [],
      level: 0,
      isAgent: span.name.includes("agent_run"),
      agentName: span.name.includes("agent_run") ? span.name.match(/\[(.+?)\]/)?.[1] : undefined,
    }));

    if (process.env.NODE_ENV === 'development') {
      console.log("All spans for visualization:", allSpans);
    }

    switch (viewMode) {
      case "timeline":
        return renderTimelineView(allSpans);
      case "tree":
        return (
          <div>
            <h4 className="font-medium text-sm text-foreground-700 mb-3">Execution Hierarchy</h4>
            {renderTreeView(processedSpans)}
          </div>
        );
      case "raw":
        return (
          <ReactJsonView
            collapsed={2}
            displayDataTypes={false}
            displayObjectSize={true}
            enableClipboard={true}
            indentWidth={2}
            name="trace"
            src={traceData || {}}
            style={{
              backgroundColor: "transparent",
              fontSize: "13px",
            }}
            theme="rjv-default"
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
          Session Trace
        </h2>
        <div className="flex items-center gap-3">
          <div className="flex glass-panel rounded-lg p-1">
            <button
              className={`px-4 py-2 text-sm rounded-md transition-all duration-300 ${
                viewMode === "timeline"
                  ? "bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white shadow-lg border border-white/20"
                  : "hover:bg-white/5 text-foreground-400 hover:text-white"
              }`}
              onClick={() => setViewMode("timeline")}
            >
              Timeline
            </button>
            <button
              className={`px-4 py-2 text-sm transition-all duration-300 ${
                viewMode === "tree"
                  ? "bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white shadow-lg border border-white/20"
                  : "hover:bg-white/5 text-foreground-400 hover:text-white"
              }`}
              onClick={() => setViewMode("tree")}
            >
              Tree
            </button>
            <button
              className={`px-4 py-2 text-sm rounded-md transition-all duration-300 ${
                viewMode === "raw"
                  ? "bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white shadow-lg border border-white/20"
                  : "hover:bg-white/5 text-foreground-400 hover:text-white"
              }`}
              onClick={() => setViewMode("raw")}
            >
              Raw
            </button>
          </div>
          <button
            className="px-4 py-2 text-sm bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all hover:scale-105 shadow-lg"
            disabled={isLoading || !sessionId}
            onClick={handleRefresh}
          >
            {isLoading ? "Loading..." : "Refresh"}
          </button>
        </div>
      </div>

      <p className="text-foreground-400 text-sm">
        Debug trace information showing agent execution timeline and system spans.
      </p>

      {!sessionId && (
        <div className="glass-panel p-6 rounded-xl border border-white/10">
          <p className="text-center text-foreground-400">
            No session available. Start a conversation to view trace data.
          </p>
        </div>
      )}

      {error && (
        <div className="glass-panel p-6 rounded-xl border border-red-500/30">
          <p className="text-center text-red-300">Error loading trace data: {error}</p>
          <button
            className="mt-3 px-4 py-2 text-sm bg-red-500/20 text-red-300 rounded-lg hover:bg-red-500/30 border border-red-500/30 block mx-auto transition-all hover:scale-105"
            onClick={handleRefresh}
          >
            Retry
          </button>
        </div>
      )}

      {isLoading && (
        <div className="glass-panel p-6 rounded-xl border border-white/10">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 mb-4 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center animate-pulse">
              <svg
                className="w-6 h-6 text-blue-400 animate-spin"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                />
              </svg>
            </div>
            <p className="text-foreground-400">Loading trace data...</p>
          </div>
        </div>
      )}

      {traceData && !isLoading && !error && (
        <div className="glass-panel rounded-xl border border-white/10 p-6">
          <div className="mb-4 text-sm text-foreground-400">
            Session ID:{" "}
            <code className="glass-panel px-2 py-1 rounded text-blue-300">{sessionId}</code>
            {traceData.trace && (
              <span className="ml-4">
                Spans: <span className="font-medium">{traceData.trace.length}</span>
              </span>
            )}
          </div>
          {renderContent()}
        </div>
      )}

      {sessionId && !traceData && !isLoading && !error && (
        <div className="glass-panel p-6 rounded-xl border border-white/10">
          <p className="text-center text-foreground-400">
            No trace data available for this session.
          </p>
        </div>
      )}
    </div>
  );
};

export default TraceTab;
