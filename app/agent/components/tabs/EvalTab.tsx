"use client";

import { <PERSON>, CardB<PERSON>, CardHeader } from "@heroui/card";
import { Chip } from "@heroui/chip";
import { Progress } from "@heroui/progress";
import { Button } from "@heroui/button";
import React, { useEffect, useState, useMemo, useCallback, useRef } from "react";

import { useAgentStore } from "../../store/agentStore";
import { EvaluationData } from "../../types";
import { calculateAllMetrics } from "../../utils/evalUtils";

const EvalTab: React.FC = () => {
  const eventHistory = useAgentStore((state) => state.eventHistory);
  const [evaluationData, setEvaluationData] = useState<EvaluationData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Track the last processed event count to avoid unnecessary recalculations
  const lastProcessedCount = useRef<number>(0);
  const lastCalculationTime = useRef<number>(0);

  // Debounce calculations to avoid excessive recalculation during rapid events
  const CALCULATION_DEBOUNCE_MS = 500; // Wait 500ms after last event before recalculating
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  // Memoize event history length and hash to detect meaningful changes
  const eventHistoryMeta = useMemo(() => {
    const length = eventHistory.length;
    const hasUsageData = eventHistory.some(event => event.usageMetadata);
    const lastEventTimestamp = eventHistory[length - 1]?.timestamp || 0;

    return {
      length,
      hasUsageData,
      lastEventTimestamp,
      // Create a simple hash to detect content changes beyond just length
      contentHash: eventHistory.slice(-5).map(e => `${e.type}-${e.author}-${e.usageMetadata?.promptTokenCount || 0}`).join('|')
    };
  }, [eventHistory]);

  // Optimized calculation function with debouncing
  const calculateMetrics = useCallback(async (force: boolean = false) => {
    const now = Date.now();

    // Skip if we've calculated recently and no significant changes
    if (!force &&
        eventHistoryMeta.length === lastProcessedCount.current &&
        (now - lastCalculationTime.current) < 2000) {
      return;
    }

    // Clear any existing debounce timer
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    // Debounce the calculation
    debounceTimer.current = setTimeout(async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log(`EvalTab: Processing ${eventHistoryMeta.length} events (was ${lastProcessedCount.current})`);
        const startTime = performance.now();

        const metrics = await calculateAllMetrics(eventHistory);

        const endTime = performance.now();
        console.log(`EvalTab: Calculated metrics in ${(endTime - startTime).toFixed(2)}ms`);

        setEvaluationData(metrics);
        lastProcessedCount.current = eventHistoryMeta.length;
        lastCalculationTime.current = now;
      } catch (err) {
        console.error("EvalTab: Error calculating metrics:", err);
        setError(err instanceof Error ? err.message : 'Failed to calculate metrics');
      } finally {
        setIsLoading(false);
      }
    }, CALCULATION_DEBOUNCE_MS);
  }, [eventHistory, eventHistoryMeta]);

  // Effect to trigger calculations when event history changes meaningfully
  useEffect(() => {
    // Only recalculate if there are meaningful changes
    if (eventHistoryMeta.length > lastProcessedCount.current ||
        (eventHistoryMeta.length > 0 && !evaluationData)) {
      calculateMetrics();
    }
  }, [eventHistoryMeta, calculateMetrics, evaluationData]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);

  const formatNullableNumber = (value: number | null, unit: string = "", decimals: number = 0) => {
    if (value === null || typeof value === "undefined") return "N/A";

    return `${value.toFixed(decimals)} ${unit}`.trim();
  };

  const formatCurrency = (value: number | null, decimals: number = 4) => {
    if (value === null || typeof value === "undefined") return "N/A";

    return `$${value.toFixed(decimals)}`;
  };

  const formatMsToSeconds = (ms: number | null, decimals: number = 2) => {
    if (ms === null || typeof ms === "undefined") return "N/A";

    return `${(ms / 1000).toFixed(decimals)} s`;
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500" />
        <span className="ml-3 text-foreground-600">Calculating metrics...</span>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-danger-500 text-lg font-medium mb-2">Error</div>
          <div className="text-foreground-600">{error}</div>
        </div>
      </div>
    );
  }

  // Show empty state
  if (!evaluationData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-foreground-500 text-lg font-medium mb-2">No Data</div>
          <div className="text-foreground-400">No evaluation data available</div>
        </div>
      </div>
    );
  }

  const { performanceMetrics, modelPerformanceMetrics, sessionMetrics, costBreakdown } = evaluationData;

  // Calculate progress percentages for visual indicators
  const tokenUsageProgress = modelPerformanceMetrics.totalTokenUsage
    ? Math.min((modelPerformanceMetrics.totalTokenUsage / 100000) * 100, 100)
    : 0;

  const costProgress = modelPerformanceMetrics.estimatedCost
    ? Math.min((modelPerformanceMetrics.estimatedCost / 1) * 100, 100)
    : 0;

  return (
    <div className="space-y-6">
      <div>
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-2xl font-bold">Evaluation</h2>
          <div className="flex items-center gap-2">
            {isLoading && (
              <div className="flex items-center gap-2 text-sm text-foreground-500">
                <div className="w-2 h-2 rounded-full bg-blue-400 animate-pulse" />
                Updating...
              </div>
            )}
            <Button
              size="sm"
              variant="ghost"
              isIconOnly
              isLoading={isLoading}
              onPress={() => calculateMetrics(true)}
              className="text-xs"
              aria-label="Refresh metrics"
            >
              {!isLoading && (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              )}
            </Button>
            <Chip
              color="success"
              size="sm"
              variant="flat"
              className="text-xs"
            >
              Live Data
            </Chip>
          </div>
        </div>
        <p className="text-foreground-500">
          Performance metrics and analysis for the Customer Due Diligence Agent.
          <span className="text-xs text-foreground-400 ml-2">
            ({eventHistoryMeta.length} events processed)
          </span>
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Current Query Card */}
        <Card className="h-fit bg-content1" shadow="sm">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold">Current Query</h3>
              <Chip color="primary" size="sm" variant="flat">
                Active
              </Chip>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-foreground-600">Tools Used</span>
                <Chip
                  color={performanceMetrics.toolsUsedCount > 0 ? "success" : "default"}
                  size="sm"
                  variant="bordered"
                >
                  {performanceMetrics.toolsUsedCount}
                </Chip>
              </div>

              <div className="text-center">
                <p className="text-sm text-foreground-500">Query performance metrics</p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Current Query Tokens Card */}
        <Card className="h-fit bg-content1" shadow="sm">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold">Token Usage</h3>
              <Chip
                color={tokenUsageProgress > 80 ? "warning" : "success"}
                size="sm"
                variant="flat"
              >
                {formatNullableNumber(modelPerformanceMetrics.totalTokenUsage, "tokens")}
              </Chip>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-4">
              <Progress
                className="max-w-md"
                color={tokenUsageProgress > 80 ? "warning" : "success"}
                label="Token Usage"
                showValueLabel={true}
                size="sm"
                value={tokenUsageProgress}
              />

              <div className="space-y-3">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-foreground-600">Prompt Tokens</span>
                  <span className="font-medium">
                    {formatNullableNumber(modelPerformanceMetrics.promptTokenUsage, "tokens")}
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-foreground-600">Completion Tokens</span>
                  <span className="font-medium">
                    {formatNullableNumber(modelPerformanceMetrics.completionTokenUsage, "tokens")}
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-foreground-700 font-medium">Estimated Cost</span>
                  <Chip color={costProgress > 80 ? "danger" : "success"} size="sm" variant="flat">
                    {formatCurrency(modelPerformanceMetrics.estimatedCost)}
                  </Chip>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-foreground-600">Interaction Duration</span>
                  <span className="font-medium">
                    {formatMsToSeconds(modelPerformanceMetrics.agentInteractionDuration)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-foreground-600">Tool Execution Time</span>
                  <span className="font-medium">
                    {formatMsToSeconds(modelPerformanceMetrics.totalToolExecutionTime)}
                  </span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Session Totals Card */}
        <Card className="h-fit bg-content1" shadow="sm">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold">Session Totals</h3>
              <Chip color="secondary" size="sm" variant="flat">
                {sessionMetrics.totalQueries} queries
              </Chip>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-3">
                <div className="flex justify-between items-center p-2 rounded bg-content2">
                  <span className="text-foreground-600">Total Queries</span>
                  <span className="font-bold text-lg">{sessionMetrics.totalQueries}</span>
                </div>
                <div className="flex justify-between items-center p-2 rounded bg-content2">
                  <span className="text-foreground-600">Total Tokens</span>
                  <span className="font-medium">
                    {formatNullableNumber(sessionMetrics.totalTokenUsage, "tokens")}
                  </span>
                </div>
                <div className="flex justify-between items-center p-2 rounded bg-content2">
                  <span className="text-foreground-600">Total Cost</span>
                  <Chip color="success" size="sm" variant="bordered">
                    {formatCurrency(sessionMetrics.totalCost)}
                  </Chip>
                </div>
              </div>

              <div className="text-center">
                <p className="text-sm text-foreground-500">Session performance summary</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Cost Breakdown by Agent */}
      {costBreakdown && costBreakdown.agentCosts.length > 0 && (
        <div className="mt-6">
          <h3 className="text-xl font-bold mb-4">Cost Breakdown by Agent</h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Agent Cost Details */}
            <Card className="bg-content1" shadow="sm">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between w-full">
                  <h4 className="text-lg font-semibold">Agent Costs</h4>
                  <Chip color="primary" size="sm" variant="flat">
                    {costBreakdown.agentCosts.length} agents
                  </Chip>
                </div>
              </CardHeader>
              <CardBody className="pt-0">
                <div className="space-y-3">
                  {costBreakdown.agentCosts.map((agent, index) => (
                    <div key={agent.agentName} className="p-3 rounded-lg bg-content2 border border-divider">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-foreground">{agent.displayName}</span>
                          <Chip
                            color={agent.modelUsed === 'gemini-2.5-pro' ? 'warning' : 'success'}
                            size="sm"
                            variant="flat"
                          >
                            {agent.modelUsed}
                          </Chip>
                        </div>
                        <Chip
                          color={agent.estimatedCost > 0.01 ? 'danger' : 'success'}
                          size="sm"
                          variant="bordered"
                        >
                          {formatCurrency(agent.estimatedCost)}
                        </Chip>
                      </div>

                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="text-center p-2 rounded bg-content3">
                          <div className="text-foreground-500">Tokens</div>
                          <div className="font-medium">{agent.totalTokens.toLocaleString()}</div>
                        </div>
                        <div className="text-center p-2 rounded bg-content3">
                          <div className="text-foreground-500">Events</div>
                          <div className="font-medium">{agent.eventCount}</div>
                        </div>
                        <div className="text-center p-2 rounded bg-content3">
                          <div className="text-foreground-500">Cost/Token</div>
                          <div className="font-medium">
                            {agent.totalTokens > 0 ?
                              `$${(agent.estimatedCost / agent.totalTokens * 1000).toFixed(4)}/k` :
                              'N/A'
                            }
                          </div>
                        </div>
                      </div>

                      {/* Token breakdown */}
                      <div className="mt-2 flex justify-between text-xs text-foreground-500">
                        <span>Input: {agent.promptTokens.toLocaleString()}</span>
                        <span>Output: {agent.completionTokens.toLocaleString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* Cost Summary */}
            <Card className="bg-content1" shadow="sm">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between w-full">
                  <h4 className="text-lg font-semibold">Cost Summary</h4>
                  <Chip color="secondary" size="sm" variant="flat">
                    Total
                  </Chip>
                </div>
              </CardHeader>
              <CardBody className="pt-0">
                <div className="space-y-4">
                  {/* Total Cost */}
                  <div className="p-4 rounded-lg bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-white/20">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground mb-1">
                        {formatCurrency(costBreakdown.totalCost)}
                      </div>
                      <div className="text-sm text-foreground-500">Total Session Cost</div>
                    </div>
                  </div>

                  {/* Model Usage Breakdown */}
                  <div className="space-y-3">
                    <h5 className="font-medium text-foreground">Model Usage</h5>
                    {(() => {
                      const modelStats = costBreakdown.agentCosts.reduce((acc, agent) => {
                        if (!acc[agent.modelUsed]) {
                          acc[agent.modelUsed] = { cost: 0, tokens: 0, agents: 0 };
                        }
                        acc[agent.modelUsed].cost += agent.estimatedCost;
                        acc[agent.modelUsed].tokens += agent.totalTokens;
                        acc[agent.modelUsed].agents += 1;
                        return acc;
                      }, {} as Record<string, { cost: number; tokens: number; agents: number }>);

                      return Object.entries(modelStats).map(([model, stats]) => (
                        <div key={model} className="flex justify-between items-center p-2 rounded bg-content2">
                          <div>
                            <div className="font-medium">{model}</div>
                            <div className="text-xs text-foreground-500">
                              {stats.agents} agent{stats.agents !== 1 ? 's' : ''} • {stats.tokens.toLocaleString()} tokens
                            </div>
                          </div>
                          <Chip
                            color={model === 'gemini-2.5-pro' ? 'warning' : 'success'}
                            size="sm"
                            variant="bordered"
                          >
                            {formatCurrency(stats.cost)}
                          </Chip>
                        </div>
                      ));
                    })()}
                  </div>

                  {/* Cost Efficiency */}
                  <div className="p-3 rounded-lg bg-content2">
                    <div className="text-sm font-medium text-foreground mb-2">Cost Efficiency</div>
                    <div className="space-y-1 text-xs">
                      <div className="flex justify-between">
                        <span className="text-foreground-500">Cost per 1K tokens:</span>
                        <span className="font-medium">
                          ${((costBreakdown.totalCost / costBreakdown.totalTokens) * 1000).toFixed(4)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-foreground-500">Total tokens:</span>
                        <span className="font-medium">{costBreakdown.totalTokens.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
};

export default EvalTab;
