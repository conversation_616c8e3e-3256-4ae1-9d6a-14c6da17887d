"use client";

import React, { useMemo, useState, useCallback } from "react";

import { ALL_AGENT_CONFIGS } from "../../config/agentConfig"; // Added
import { useAgentStore } from "../../store/agentStore";
import { AgentName, EventType } from "../../types"; // Added AgentName
import EventItem from "../EventItem";

const EventsTab: React.FC = () => {
  const [filterAgent, setFilterAgent] = useState<AgentName | null>(null); // Changed type to AgentName
  const [filterType, setFilterType] = useState<string | null>(null);
  const [showOnlyErrors, setShowOnlyErrors] = useState<boolean>(false);

  // Use individual selectors with useMemo to prevent unnecessary re-renders
  const eventHistory = useAgentStore((state) => state.eventHistory);
  const filterEvents = useAgentStore((state) => state.filterEvents);

  // Memoize these references to avoid issues in server components
  const memoizedEventHistory = useMemo(() => eventHistory, [eventHistory]);
  const memoizedFilterEvents = useMemo(() => filterEvents, [filterEvents]);



  // Event type options
  const eventTypes = [
    { id: "model", label: "Agent Output" },
    { id: "tool", label: "Tool Calls" },
    { id: "user", label: "Tool Responses" },
  ];

  // Optimized filter functions with useCallback
  const clearFilters = useCallback(() => {
    setFilterAgent(null);
    setFilterType(null);
    setShowOnlyErrors(false);
  }, []);

  const handleAgentFilterChange = useCallback((value: string) => {
    setFilterAgent(value ? (value as AgentName) : null);
  }, []);

  const handleTypeFilterChange = useCallback((value: string) => {
    setFilterType(value || null);
  }, []);

  const handleErrorFilterChange = useCallback((checked: boolean) => {
    setShowOnlyErrors(checked);
  }, []);

  // Filter events based on current filters with performance optimization
  const filteredEvents = useMemo(() => {
    // Convert filterType to EventType safely
    let eventType: EventType | undefined = undefined;

    if (filterType) {
      // Type assertion through unknown as recommended for safety
      eventType = filterType as unknown as EventType;
    }

    return memoizedFilterEvents({
      author: filterAgent || undefined,
      type: eventType,
      isError: showOnlyErrors ? true : undefined,
    });
  }, [memoizedFilterEvents, filterAgent, filterType, showOnlyErrors, memoizedEventHistory]);

  if (memoizedEventHistory.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-[60vh] text-foreground-400">
        <div className="w-24 h-24 mb-6 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
          <svg
            className="w-12 h-12 text-blue-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
          </svg>
        </div>
        <p className="text-lg">No events yet</p>
        <p className="text-sm text-foreground-500 mt-2">Start a new query to see events</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 h-full">
      <div className="flex flex-col">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
          Events
        </h2>
        <p className="text-sm text-foreground-400 mt-1">
          <span className="font-mono">{memoizedEventHistory.length}</span> events total •{" "}
          <span className="font-mono">{filteredEvents.length}</span> events displayed
        </p>
      </div>

      {/* Filter controls */}
      <div className="glass-panel p-4 rounded-xl border border-white/10">
        <div className="text-sm font-semibold text-white mb-3 flex items-center gap-2">
          <svg
            className="w-4 h-4 text-blue-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
          </svg>
          Filters
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {/* Agent filter */}
          <div>
            <label className="block text-xs text-foreground-500 mb-1" htmlFor="agent-filter">
              Agent
            </label>
            <select
              className="w-full px-3 py-2 rounded-lg glass-panel border border-white/10 text-white focus:border-blue-400/50 transition-colors"
              id="agent-filter"
              value={filterAgent || ""} // Keep string for select value
              onChange={(e) => handleAgentFilterChange(e.target.value)}
            >
              <option value="">All Agents</option>
              {ALL_AGENT_CONFIGS.map((config) => (
                <option key={config.id} value={config.id}>
                  {config.displayName}
                </option>
              ))}
            </select>
          </div>

          {/* Event type filter */}
          <div>
            <label className="block text-xs text-foreground-500 mb-1" htmlFor="event-type-filter">
              Event Type
            </label>
            <select
              className="w-full px-3 py-2 rounded-lg glass-panel border border-white/10 text-white focus:border-blue-400/50 transition-colors"
              id="event-type-filter"
              value={filterType || ""}
              onChange={(e) => handleTypeFilterChange(e.target.value)}
            >
              <option value="">All Types</option>
              {eventTypes.map((type) => (
                <option key={type.id} value={type.id}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* Error filter */}
          <div className="flex items-end">
            <label className="flex items-center cursor-pointer group" htmlFor="show-errors-filter">
              <input
                checked={showOnlyErrors}
                className="mr-2 w-4 h-4 rounded border-white/20 bg-transparent checked:bg-red-500 checked:border-red-500 focus:ring-red-500 focus:ring-offset-0"
                id="show-errors-filter"
                type="checkbox"
                onChange={(e) => handleErrorFilterChange(e.target.checked)}
              />
              <span className="text-sm text-foreground-300 group-hover:text-white transition-colors">
                Show Only Errors
              </span>
            </label>
          </div>
        </div>
      </div>

      {/* Event list */}
      <div className="flex-1 overflow-y-auto">
        {filteredEvents.length > 0 ? (
          <div className="space-y-2">
            {filteredEvents.map((event) => (
              <EventItem key={event.id} event={event} />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-64 glass-panel rounded-xl border border-white/10 p-8">
            <svg
              className="w-16 h-16 text-foreground-400 mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
              />
            </svg>
            <p className="text-foreground-300 mb-4">No events match the current filters</p>
            <button
              className="px-4 py-2 text-sm bg-gradient-to-r from-blue-500/20 to-purple-500/20 hover:from-blue-500/30 hover:to-purple-500/30 text-white rounded-lg border border-white/20 transition-all hover:scale-105"
              onClick={clearFilters}
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default EventsTab;
