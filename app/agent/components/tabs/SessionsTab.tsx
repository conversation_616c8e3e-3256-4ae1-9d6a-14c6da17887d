"use client";

import React, { useEffect, useState, useCallback } from "react";

import { useAgentStore } from "../../store/agentStore";
import { SessionHistoryEntry } from "../../types/index"; // Pointing to the consolidated types
import { getSessionHistory } from "../../utils/sessionHistoryUtils";

const SessionsTab: React.FC = () => {
  const [sessions, setSessions] = useState<SessionHistoryEntry[]>([]);
  const [loadingSessionId, setLoadingSessionId] = useState<string | null>(null);
  const eventHistory = useAgentStore((state) => state.eventHistory);
  const setSessionId = useAgentStore((state) => state.setSessionId);
  const clearEventHistory = useAgentStore((state) => state.clearEventHistory);

  useEffect(() => {
    setSessions(getSessionHistory());
  }, []);

  // Refresh sessions when event history changes (new session might have been added)
  useEffect(() => {
    setSessions(getSessionHistory());
  }, [eventHistory.length]);

  // Handle session loading
  const handleLoadSession = useCallback(async (sessionId: string) => {
    setLoadingSessionId(sessionId);

    try {
      // Clear current event history
      clearEventHistory();

      // Set the new session ID
      setSessionId(sessionId);

      // Note: In a real implementation, you would fetch the session data from the backend
      // For now, we'll just switch to the session ID and let the SSE connection handle it
      console.log(`Loading session: ${sessionId}`);

      // You could add a toast notification here
      // toast.success(`Switched to session ${sessionId}`);

    } catch (error) {
      console.error('Failed to load session:', error);
      // You could add error handling here
      // toast.error('Failed to load session');
    } finally {
      setLoadingSessionId(null);
    }
  }, [clearEventHistory, setSessionId]);

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Session History</h2>
      <p className="text-foreground-500 mb-4">
        This tab will display previous sessions and allow you to reload them.
      </p>

      <div className="border border-divider rounded-md overflow-hidden">
        <div className="bg-content2 p-3 border-b border-divider grid grid-cols-12 gap-4">
          <div className="col-span-2 font-medium">Date</div>
          <div className="col-span-3 font-medium">Session ID</div>
          <div className="col-span-5 font-medium">Query</div>
          <div className="col-span-2 font-medium">Actions</div>
        </div>

        <div className="divide-y divide-divider">
          {sessions.length === 0 ? (
            <div className="p-3 text-center text-foreground-500">No session history found.</div>
          ) : (
            sessions.map((session) => (
              <div key={session.id} className="p-3 grid grid-cols-12 gap-4 hover:bg-content2">
                <div className="col-span-2 text-foreground-700 text-sm">
                  {new Date(session.startTime).toLocaleString()}
                </div>
                <div
                  className="col-span-3 text-foreground-600 text-sm font-mono truncate"
                  title={session.sessionId}
                >
                  {session.sessionId}
                </div>
                <div className="col-span-5 truncate" title={session.initialQuery}>
                  {session.initialQuery}
                </div>
                <div className="col-span-2">
                  <button
                    className={`px-3 py-1 text-sm rounded transition-colors ${
                      loadingSessionId === session.sessionId
                        ? 'bg-blue-500/20 text-blue-300 cursor-not-allowed'
                        : 'text-primary hover:bg-primary/10 hover:text-primary-300'
                    }`}
                    disabled={loadingSessionId === session.sessionId}
                    onClick={() => handleLoadSession(session.sessionId)}
                  >
                    {loadingSessionId === session.sessionId ? 'Loading...' : 'Load'}
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      <div className="mt-4 text-sm text-foreground-500">
        <p>
          Displaying recent session queries from your browser&rsquo;s local storage.
          Click &quot;Load&quot; to switch to a previous session.
        </p>
        <p className="mt-2 text-xs text-foreground-400">
          Note: Loading a session will clear the current event history and switch to the selected session ID.
        </p>
      </div>
    </div>
  );
};

export default SessionsTab;
