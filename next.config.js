/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  reactStrictMode: true,

  // ESLint configuration
  eslint: {
    // Run ESLint on all directories that contain source code
    dirs: ['app', 'components', 'lib', 'utils'],

    // Don't ignore build-time ESLint errors in production
    ignoreDuringBuilds: false,
  },

  // TypeScript configuration
  typescript: {
    // Don't ignore TypeScript errors during builds
    ignoreBuildErrors: false,
  },

  // Experimental features for better Docker builds
  experimental: {
    // Reduce memory usage during builds
    workerThreads: false,
  },
};

module.exports = nextConfig;
